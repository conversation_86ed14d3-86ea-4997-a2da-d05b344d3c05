%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-6242925268469885464
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0bf0785297c514c44b7678a48519eb23, type: 3}
  m_Name: "\u65BD\u6CD5\u7279\u65482 (2)"
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 2250557162536207891}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 0.8666666666666667
    m_ClipIn: 0.34999986489613844
    m_Asset: {fileID: -2776848209972672535}
    m_Duration: 0.5
    m_TimeScale: 1
    m_ParentTrack: {fileID: -6242925268469885464}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: -1
    m_BlendOutDuration: -1
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: "\u66B4\u6012\u59FF\u6001\u65BD\u6CD5\u7279\u6548"
  m_Markers:
    m_Objects: []
--- !u!114 &-5368926833038885687
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8193a6916505c16468554e2c0ef6b1fe, type: 3}
  m_Name: BattlePlayableAsset_Control
  m_EditorClassIdentifier: 
  position: {x: 0, y: 2.34, z: 0.54}
  scale: {x: 0.8, y: 0.8, z: 0.8}
  rotation: {x: 90, y: 0, z: 0}
  changeRotation: 0
  effectPoint: {x: 0, y: 0, z: 0}
  sourceGameObject:
    exposedName: 9397b7270f9ed694d9fe5d635751427a
    defaultValue: {fileID: 0}
  prefabGameObject: {fileID: 1492898452751506, guid: 6301d033b444b6f488060b710b2930a9,
    type: 3}
  updateParticle: 1
  particleRandomSeed: 1229
  updateDirector: 1
  updateITimeControl: 1
  searchHierarchy: 0
  active: 1
  postPlayback: 2
--- !u!114 &-2776848209972672535
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8193a6916505c16468554e2c0ef6b1fe, type: 3}
  m_Name: BattlePlayableAsset_Control
  m_EditorClassIdentifier: 
  position: {x: -0.15, y: 2.3, z: -0.49}
  scale: {x: 0.8, y: 0.8, z: 0.8}
  rotation: {x: 0, y: 0, z: 0}
  changeRotation: 1
  effectPoint: {x: 0, y: 0, z: 0}
  sourceGameObject:
    exposedName: 26454fc83ad17624189c384e75f5176b
    defaultValue: {fileID: 0}
  prefabGameObject: {fileID: 4069618285410288219, guid: 9a0e4ddec4c262b408ed2c0d37f685c3,
    type: 3}
  updateParticle: 1
  particleRandomSeed: 9010
  updateDirector: 1
  updateITimeControl: 1
  searchHierarchy: 0
  active: 1
  postPlayback: 2
--- !u!114 &-598405254058685954
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8abdae81ccd3ebf43bb7a0790c551176, type: 3}
  m_Name: BattlePlayableAsset_PlaySE(Clone)(Clone)(Clone)(Clone)(Clone)(Clone)(Clone)(Clone)(Clone)(Clone)
  m_EditorClassIdentifier: 
  sourceGameObject:
    exposedName: 2e8ee9c8a6f835d4a92a38ea963690c1
    defaultValue: {fileID: 0}
  toPlaySE: 1
  seKey: "\u7834\u98CE1"
  bgmKey: 
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: bfda56da833e2384a9677cd3c976a436, type: 3}
  m_Name: "\u6218\u6597Timline_\u66B4\u6012\u59FF\u6001Timeline"
  m_EditorClassIdentifier: 
  m_Version: 0
  m_Tracks:
  - {fileID: 2250557162536207891}
  - {fileID: 8364546783546903734}
  m_FixedDuration: 0
  m_EditorSettings:
    m_Framerate: 60
    m_ScenePreview: 1
  m_DurationMode: 0
  m_MarkerTrack: {fileID: 0}
--- !u!114 &197468181708446978
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a11da72315417c54781b97c97fc03a87, type: 3}
  m_Name: BattlePlayableAsset_SpineAnimation
  m_EditorClassIdentifier: 
  sourceGameObject:
    exposedName: 1932c16824c8b89449b831f6795e3614
    defaultValue: {fileID: 0}
  spineAnimationState:
    behaviorTargetPrefab:
      exposedName: 
      defaultValue: {fileID: 0}
    position: {x: 0, y: 0, z: 0}
    scale: {x: 0, y: 0, z: 0}
    rotation: {x: 0, y: 0, z: 0}
    unit: {fileID: 0}
    skeletonAnimation: {fileID: 0}
    spineAnimTrueName: 
    formerPoseName: 
    spinePlaySpeed: 1
  animationState:
    behaviorTargetPrefab:
      exposedName: 
      defaultValue: {fileID: 0}
    position: {x: 0, y: 0, z: 0}
    scale: {x: 0, y: 0, z: 0}
    rotation: {x: 0, y: 0, z: 0}
    unit: {fileID: 0}
    skeletonAnimation: {fileID: 0}
    spineAnimTrueName: Huge_Cast
    formerPoseName: Huge_Idle
    spinePlaySpeed: 1
  spineAniName: "\u65BD\u6CD5"
  weaponTypeName: "\u53CC\u624B\u5251"
  useCustomWeapon: 0
  spinePlaySpeed: 1
--- !u!114 &1414455263980650033
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a11da72315417c54781b97c97fc03a87, type: 3}
  m_Name: BattlePlayableAsset_SpineAnimation
  m_EditorClassIdentifier: 
  sourceGameObject:
    exposedName: 74864376e9af05146914f858ecb16133
    defaultValue: {fileID: 0}
  spineAnimationState:
    behaviorTargetPrefab:
      exposedName: 
      defaultValue: {fileID: 0}
    position: {x: 0, y: 0, z: 0}
    scale: {x: 0, y: 0, z: 0}
    rotation: {x: 0, y: 0, z: 0}
    unit: {fileID: 0}
    skeletonAnimation: {fileID: 0}
    spineAnimTrueName: 
    formerPoseName: 
    spinePlaySpeed: 1
  animationState:
    behaviorTargetPrefab:
      exposedName: 
      defaultValue: {fileID: 0}
    position: {x: 0, y: 0, z: 0}
    scale: {x: 0, y: 0, z: 0}
    rotation: {x: 0, y: 0, z: 0}
    unit: {fileID: 0}
    skeletonAnimation: {fileID: 0}
    spineAnimTrueName: Huge_Idle
    formerPoseName: Huge_Idle
    spinePlaySpeed: 1
  spineAniName: "\u5F85\u673A1"
  weaponTypeName: "\u5251\u76FE"
  useCustomWeapon: 0
  spinePlaySpeed: 1
--- !u!114 &2118249636089913807
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fee833bea217ac74894dad39ccfe5c0f, type: 3}
  m_Name: "\u97F3\u6548"
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 2250557162536207891}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 0.11666666666666667
    m_ClipIn: 0
    m_Asset: {fileID: -598405254058685954}
    m_Duration: 0.3666666666666667
    m_TimeScale: 1
    m_ParentTrack: {fileID: 2118249636089913807}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: -1
    m_BlendOutDuration: -1
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: BattlePlayableAsset_PlaySE
  - m_Version: 1
    m_Start: 0.8500001351038614
    m_ClipIn: 0
    m_Asset: {fileID: 3711686078835851204}
    m_Duration: 0.8833333333333333
    m_TimeScale: 1
    m_ParentTrack: {fileID: 2118249636089913807}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: -1
    m_BlendOutDuration: -1
    m_MixInCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: BattlePlayableAsset_PlaySE
  m_Markers:
    m_Objects: []
--- !u!114 &2250557162536207891
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0fc6f5187a81dc47999eefade6f0935, type: 3}
  m_Name: "\u65BD\u6CD5\u8005"
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 11400000}
  m_Children:
  - {fileID: 5622222551031462675}
  - {fileID: -6242925268469885464}
  - {fileID: 6492833417866953444}
  - {fileID: 2118249636089913807}
  m_Clips: []
  m_Markers:
    m_Objects: []
--- !u!114 &3711686078835851204
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8abdae81ccd3ebf43bb7a0790c551176, type: 3}
  m_Name: BattlePlayableAsset_PlaySE(Clone)(Clone)(Clone)(Clone)(Clone)(Clone)(Clone)(Clone)
  m_EditorClassIdentifier: 
  sourceGameObject:
    exposedName: 96e044277ec83db4fa9d40632eb761e9
    defaultValue: {fileID: 0}
  toPlaySE: 1
  seKey: "\u9B54\u6CD5_\u90AA\u60762"
  bgmKey: 
--- !u!114 &3924848909126453453
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 53462d5107f1f4e48b0da2816d3c6136, type: 3}
  m_Name: "\u53D7\u51FB\u52A8\u4F5C1"
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 1
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 8364546783546903734}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 0.48333333333333334
    m_ClipIn: 0
    m_Asset: {fileID: 1414455263980650033}
    m_Duration: 0.6666666865348817
    m_TimeScale: 1
    m_ParentTrack: {fileID: 3924848909126453453}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: -1
    m_BlendOutDuration: -1
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: BattlePlayableAsset_SpineAnimation
  m_Markers:
    m_Objects: []
  trackIndex: 0
  unscaledTime: 0
--- !u!114 &5622222551031462675
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 53462d5107f1f4e48b0da2816d3c6136, type: 3}
  m_Name: "\u65BD\u6CD5\u52A8\u4F5C1"
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 2250557162536207891}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 0
    m_ClipIn: 0
    m_Asset: {fileID: 197468181708446978}
    m_Duration: 1.7333334684371948
    m_TimeScale: 1
    m_ParentTrack: {fileID: 5622222551031462675}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: -1
    m_BlendOutDuration: -1
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: BattlePlayableAsset_SpineAnimation
  m_Markers:
    m_Objects: []
  trackIndex: 0
  unscaledTime: 0
--- !u!114 &6492833417866953444
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 58ee5c78e37282048abe9cdfee9e0b1b, type: 3}
  m_Name: "\u9707\u5C4F"
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 2250557162536207891}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 0.8666666666666667
    m_ClipIn: 0
    m_Asset: {fileID: 7195512231661400737}
    m_Duration: 0.5
    m_TimeScale: 1
    m_ParentTrack: {fileID: 6492833417866953444}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: -1
    m_BlendOutDuration: -1
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: BattlePlayableAsset_CameraShake
  m_Markers:
    m_Objects: []
--- !u!114 &7195512231661400737
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c1b19e35d610444cb5d6902cee6cab9, type: 3}
  m_Name: BattlePlayableAsset_CameraShake
  m_EditorClassIdentifier: 
  sourceGameObject:
    exposedName: 
    defaultValue: {fileID: 0}
  cameraShakeType: 1
--- !u!114 &8364546783546903734
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0fc6f5187a81dc47999eefade6f0935, type: 3}
  m_Name: "\u76EE\u6807"
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 11400000}
  m_Children:
  - {fileID: 3924848909126453453}
  - {fileID: 8487987411735357001}
  m_Clips: []
  m_Markers:
    m_Objects: []
--- !u!114 &8487987411735357001
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0bf0785297c514c44b7678a48519eb23, type: 3}
  m_Name: "\u53D7\u51FB\u52A8\u4F5C2 (1)"
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 8364546783546903734}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 0.48333333333333334
    m_ClipIn: 0
    m_Asset: {fileID: -5368926833038885687}
    m_Duration: 1.05
    m_TimeScale: 1
    m_ParentTrack: {fileID: 8487987411735357001}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: -1
    m_BlendOutDuration: -1
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: "\u66B4\u6012\u59FF\u6001\u53D7\u51FB\u7279\u6548"
  m_Markers:
    m_Objects: []
