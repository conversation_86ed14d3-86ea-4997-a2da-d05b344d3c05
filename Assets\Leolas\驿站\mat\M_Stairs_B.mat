%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-8923125811829913335
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: da692e001514ec24dbc4cca1949ff7e8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 12
  hdPluginSubTargetMaterialVersions:
    m_Keys: []
    m_Values: 
--- !u!114 &-7014379433172008305
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0353a89b1f911e48b9e16bdc9f2e058, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 7
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: M_Stairs_B
  m_Shader: {fileID: -6465566751694194690, guid: 16f07fc943a58284b9b8715135ede06f,
    type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - _ALPHATEST_ON
  m_InvalidKeywords:
  - _DISABLE_SSR_TRANSPARENT
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 1
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: 2475
  stringTagMap:
    MotionVector: User
    RenderType: TransparentCutout
  disabledShaderPasses:
  - TransparentDepthPrepass
  - TransparentDepthPostpass
  - TransparentBackface
  - RayTracingPrepass
  - MOTIONVECTORS
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _Base:
        m_Texture: {fileID: 2800000, guid: fc4d1f45efd82084c9451df8dfbb0f21, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Emissive:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Mask:
        m_Texture: {fileID: 2800000, guid: 8cd2117216f0d9d459e29b6e4bc7ced6, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Normal:
        m_Texture: {fileID: 2800000, guid: 44e170aa50f1cc74fb737dfffa900434, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ORM:
        m_Texture: {fileID: 2800000, guid: 77dafeed31353fd4d972d2f8e3fc5330, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Opacity:
        m_Texture: {fileID: 2800000, guid: 391f967c728a5ac448ae3ca563127c63, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Uni_nrm:
        m_Texture: {fileID: 2800000, guid: b82cb8c2e3c79d4498f3e565e9dac41d, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AddPrecomputedVelocity: 0
    - _AlphaClip: 1
    - _AlphaCutoffEnable: 1
    - _AlphaDstBlend: 0
    - _AlphaSrcBlend: 1
    - _AlphaToMask: 1
    - _AlphaToMaskInspectorValue: 0
    - _BUILTIN_AlphaClip: 1
    - _BUILTIN_Blend: 0
    - _BUILTIN_CullMode: 2
    - _BUILTIN_DstBlend: 0
    - _BUILTIN_QueueControl: -1
    - _BUILTIN_QueueOffset: 0
    - _BUILTIN_SrcBlend: 1
    - _BUILTIN_Surface: 0
    - _BUILTIN_ZTest: 4
    - _BUILTIN_ZWrite: 1
    - _BUILTIN_ZWriteControl: 0
    - _BaseTintColorIntensity: 0.9
    - _Blend: 0
    - _BlendMode: 0
    - _BlendModePreserveSpecular: 0
    - _CastShadows: 1
    - _ConservativeDepthOffsetEnable: 0
    - _Cull: 2
    - _CullMode: 2
    - _CullModeForward: 2
    - _DepthOffsetEnable: 0
    - _Dirt_Density: 0.55
    - _Dirt_roughness: 0.9
    - _DoubleSidedEnable: 0
    - _DoubleSidedGIMode: 0
    - _DoubleSidedNormalMode: 2
    - _DstBlend: 0
    - _EmissivePower: 1
    - _EmissiveTintIntensity: 1
    - _EnableBlendModePreserveSpecularLighting: 1
    - _EnableFogOnTransparent: 1
    - _Mask_Density: 1
    - _MaskedTintPower: 0.5
    - _Normal_Strength: 1
    - _OpaqueCullMode: 2
    - _OverallRoughness: 3
    - _QueueControl: 1
    - _QueueOffset: 0
    - _RayTracing: 0
    - _ReceiveShadows: 1
    - _ReceivesSSR: 1
    - _ReceivesSSRTransparent: 0
    - _RefractionModel: 0
    - _RenderQueueType: 1
    - _RequireSplitLighting: 0
    - _Rotation: 0
    - _Saturation: 1
    - _SrcBlend: 1
    - _StencilRef: 0
    - _StencilRefDepth: 8
    - _StencilRefDistortionVec: 4
    - _StencilRefGBuffer: 10
    - _StencilRefMV: 40
    - _StencilWriteMask: 6
    - _StencilWriteMaskDepth: 8
    - _StencilWriteMaskDistortionVec: 4
    - _StencilWriteMaskGBuffer: 14
    - _StencilWriteMaskMV: 40
    - _SupportDecals: 1
    - _Surface: 0
    - _SurfaceType: 0
    - _Tilinng: 1
    - _TransparentBackfaceEnable: 0
    - _TransparentCullMode: 2
    - _TransparentDepthPostpassEnable: 0
    - _TransparentDepthPrepassEnable: 0
    - _TransparentSortPriority: 0
    - _TransparentWritingMotionVec: 0
    - _TransparentZWrite: 0
    - _Unique_Normal_Strength: 1
    - _UseBaseAlphaAsOpacityInsteadOpacityTexture: 0
    - _UseEmissive: 0
    - _UseMask: 0
    - _UseOpacity: 0
    - _UseShadowThreshold: 0
    - _UseWind: 0
    - _Wind_Density: 0.3
    - _Wind_Speed: 0.2
    - _Wind_Strength: 0.2
    - _WorkflowMode: 1
    - _ZTest: 4
    - _ZTestDepthEqualForOpaque: 3
    - _ZTestGBuffer: 3
    - _ZTestTransparent: 4
    - _ZWrite: 1
    - _ZWriteControl: 0
    - _edge_highlight_strength: 3
    m_Colors:
    - _BaseTintColor2: {r: 0.9245283, g: 0.8931292, b: 0.8329477, a: 0}
    - _DirtColor: {r: 0.9245283, g: 0.8443952, b: 0.6497864, a: 1}
    - _DoubleSidedConstants: {r: 1, g: 1, b: -1, a: 0}
    - _EmissionColor: {r: 1, g: 1, b: 1, a: 1}
    - _EmissiveTintColor: {r: 1, g: 1, b: 1, a: 0}
    - _MaskedTintColor: {r: 1, g: 1, b: 1, a: 0}
  m_BuildTextureStacks: []
--- !u!114 &4152621865069977923
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 639247ca83abc874e893eb93af2b5e44, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 0
