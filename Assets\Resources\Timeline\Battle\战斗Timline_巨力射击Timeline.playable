%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-7172507976996212384
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fee833bea217ac74894dad39ccfe5c0f, type: 3}
  m_Name: "\u97F3\u6548"
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 2250557162536207891}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 0
    m_ClipIn: 0
    m_Asset: {fileID: -2515776825411635957}
    m_Duration: 0.3666666666666667
    m_TimeScale: 1
    m_ParentTrack: {fileID: -7172507976996212384}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: -1
    m_BlendOutDuration: -1
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: BattlePlayableAsset_PlaySE
  - m_Version: 1
    m_Start: 1.4833333333333334
    m_ClipIn: 0
    m_Asset: {fileID: 8539448655968932695}
    m_Duration: 0.8833333333333333
    m_TimeScale: 1
    m_ParentTrack: {fileID: -7172507976996212384}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: -1
    m_BlendOutDuration: -1
    m_MixInCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: BattlePlayableAsset_PlaySE
  m_Markers:
    m_Objects: []
--- !u!114 &-6729283777437973898
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8193a6916505c16468554e2c0ef6b1fe, type: 3}
  m_Name: BattlePlayableAsset_Control
  m_EditorClassIdentifier: 
  position: {x: 0, y: 0, z: 0}
  scale: {x: 1, y: 1, z: 1}
  rotation: {x: 0, y: 0, z: 0}
  changeRotation: 0
  effectPoint: {x: 0, y: 0, z: 0}
  sourceGameObject:
    exposedName: 
    defaultValue: {fileID: 0}
  prefabGameObject: {fileID: 1749888386530418, guid: 49561d868568ea84496053a9af82d52f,
    type: 3}
  updateParticle: 1
  particleRandomSeed: 1288
  updateDirector: 1
  updateITimeControl: 1
  searchHierarchy: 0
  active: 1
  postPlayback: 2
--- !u!114 &-5739147598365840991
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8193a6916505c16468554e2c0ef6b1fe, type: 3}
  m_Name: BattlePlayableAsset_Control
  m_EditorClassIdentifier: 
  position: {x: 0, y: 2.5, z: 0.54}
  scale: {x: 1, y: 1, z: 1}
  rotation: {x: 0, y: 0, z: 0}
  changeRotation: 0
  effectPoint: {x: 0, y: 0, z: 0}
  sourceGameObject:
    exposedName: 353bf4f82e3ec79448246cfb40502a03
    defaultValue: {fileID: 0}
  prefabGameObject: {fileID: 4258062595959007045, guid: b0b74ac98316e9c4787194e0c8593c6c,
    type: 3}
  updateParticle: 1
  particleRandomSeed: 1229
  updateDirector: 1
  updateITimeControl: 1
  searchHierarchy: 0
  active: 1
  postPlayback: 2
--- !u!114 &-2515776825411635957
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8abdae81ccd3ebf43bb7a0790c551176, type: 3}
  m_Name: BattlePlayableAsset_PlaySE(Clone)(Clone)(Clone)(Clone)(Clone)(Clone)(Clone)(Clone)
  m_EditorClassIdentifier: 
  sourceGameObject:
    exposedName: 3334d04497194fd4bbb9a2abb43f614b
    defaultValue: {fileID: 0}
  toPlaySE: 1
  seKey: "\u6218\u6597_\u62C9\u5F131"
  bgmKey: 
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: bfda56da833e2384a9677cd3c976a436, type: 3}
  m_Name: "\u6218\u6597Timline_\u5DE8\u529B\u5C04\u51FBTimeline"
  m_EditorClassIdentifier: 
  m_Version: 0
  m_Tracks:
  - {fileID: 2250557162536207891}
  - {fileID: 8364546783546903734}
  m_FixedDuration: 0
  m_EditorSettings:
    m_Framerate: 60
    m_ScenePreview: 1
  m_DurationMode: 0
  m_MarkerTrack: {fileID: 0}
--- !u!114 &197468181708446978
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a11da72315417c54781b97c97fc03a87, type: 3}
  m_Name: BattlePlayableAsset_SpineAnimation
  m_EditorClassIdentifier: 
  sourceGameObject:
    exposedName: 1932c16824c8b89449b831f6795e3614
    defaultValue: {fileID: 0}
  spineAnimationState:
    behaviorTargetPrefab:
      exposedName: 
      defaultValue: {fileID: 0}
    position: {x: 0, y: 0, z: 0}
    scale: {x: 0, y: 0, z: 0}
    rotation: {x: 0, y: 0, z: 0}
    unit: {fileID: 0}
    skeletonAnimation: {fileID: 0}
    spineAnimTrueName: 
    formerPoseName: 
    spinePlaySpeed: 1
  animationState:
    behaviorTargetPrefab:
      exposedName: 
      defaultValue: {fileID: 0}
    position: {x: 0, y: 0, z: 0}
    scale: {x: 0, y: 0, z: 0}
    rotation: {x: 0, y: 0, z: 0}
    unit: {fileID: 0}
    skeletonAnimation: {fileID: 0}
    spineAnimTrueName: Bow_Atk3
    formerPoseName: Bow_Idle
    spinePlaySpeed: 1
  spineAniName: "\u653B\u51FB3"
  weaponTypeName: "\u5F13\u7BAD"
  useCustomWeapon: 0
  spinePlaySpeed: 1
--- !u!114 &1414455263980650033
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a11da72315417c54781b97c97fc03a87, type: 3}
  m_Name: BattlePlayableAsset_SpineAnimation
  m_EditorClassIdentifier: 
  sourceGameObject:
    exposedName: 74864376e9af05146914f858ecb16133
    defaultValue: {fileID: 0}
  spineAnimationState:
    behaviorTargetPrefab:
      exposedName: 
      defaultValue: {fileID: 0}
    position: {x: 0, y: 0, z: 0}
    scale: {x: 0, y: 0, z: 0}
    rotation: {x: 0, y: 0, z: 0}
    unit: {fileID: 0}
    skeletonAnimation: {fileID: 0}
    spineAnimTrueName: 
    formerPoseName: 
    spinePlaySpeed: 1
  animationState:
    behaviorTargetPrefab:
      exposedName: 
      defaultValue: {fileID: 0}
    position: {x: 0, y: 0, z: 0}
    scale: {x: 0, y: 0, z: 0}
    rotation: {x: 0, y: 0, z: 0}
    unit: {fileID: 0}
    skeletonAnimation: {fileID: 0}
    spineAnimTrueName: hurt
    formerPoseName: B_idle
    spinePlaySpeed: 1
  spineAniName: "\u53D7\u4F24"
  weaponTypeName: "\u5251\u76FE"
  useCustomWeapon: 0
  spinePlaySpeed: 1
--- !u!114 &2250557162536207891
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0fc6f5187a81dc47999eefade6f0935, type: 3}
  m_Name: "\u65BD\u6CD5\u8005"
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 11400000}
  m_Children:
  - {fileID: 5622222551031462675}
  - {fileID: 8293633450796141333}
  - {fileID: 8336489461035930840}
  - {fileID: -7172507976996212384}
  m_Clips: []
  m_Markers:
    m_Objects: []
--- !u!114 &3924848909126453453
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 53462d5107f1f4e48b0da2816d3c6136, type: 3}
  m_Name: "\u53D7\u51FB\u52A8\u4F5C1"
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 8364546783546903734}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 1.75
    m_ClipIn: 0
    m_Asset: {fileID: 1414455263980650033}
    m_Duration: 0.6666666865348817
    m_TimeScale: 1
    m_ParentTrack: {fileID: 3924848909126453453}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: -1
    m_BlendOutDuration: -1
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: BattlePlayableAsset_SpineAnimation
  m_Markers:
    m_Objects: []
  trackIndex: 0
  unscaledTime: 0
--- !u!114 &4243493128814240694
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0bf0785297c514c44b7678a48519eb23, type: 3}
  m_Name: "\u53D7\u51FB\u52A8\u4F5C2 (1)"
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 8364546783546903734}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 1.75
    m_ClipIn: 0
    m_Asset: {fileID: -5739147598365840991}
    m_Duration: 1
    m_TimeScale: 1
    m_ParentTrack: {fileID: 4243493128814240694}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: -1
    m_BlendOutDuration: -1
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: "\u5DE8\u529B\u5C04\u51FB\u53D7\u51FB\u7279\u6548"
  m_Markers:
    m_Objects: []
--- !u!114 &5622222551031462675
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 53462d5107f1f4e48b0da2816d3c6136, type: 3}
  m_Name: "\u65BD\u6CD5\u52A8\u4F5C1"
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 2250557162536207891}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 0
    m_ClipIn: 0
    m_Asset: {fileID: 197468181708446978}
    m_Duration: 2.8333334922790527
    m_TimeScale: 1
    m_ParentTrack: {fileID: 5622222551031462675}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: -1
    m_BlendOutDuration: -1
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: BattlePlayableAsset_SpineAnimation
  m_Markers:
    m_Objects: []
  trackIndex: 0
  unscaledTime: 0
--- !u!114 &7835311218715804966
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8193a6916505c16468554e2c0ef6b1fe, type: 3}
  m_Name: BattlePlayableAsset_Control
  m_EditorClassIdentifier: 
  position: {x: 0, y: 3.12, z: -0.76}
  scale: {x: 1.5, y: 1.5, z: 1.5}
  rotation: {x: 0, y: 0, z: 0}
  changeRotation: 0
  effectPoint: {x: 0, y: 0, z: 0}
  sourceGameObject:
    exposedName: af6d1fb1ffd63f04bb0eb8da696e1349
    defaultValue: {fileID: 0}
  prefabGameObject: {fileID: 1749888386530418, guid: 8ff685664a8eac948ba9233ece7cbd17,
    type: 3}
  updateParticle: 1
  particleRandomSeed: 4524
  updateDirector: 1
  updateITimeControl: 1
  searchHierarchy: 0
  active: 1
  postPlayback: 2
--- !u!114 &8293633450796141333
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0bf0785297c514c44b7678a48519eb23, type: 3}
  m_Name: "\u65BD\u6CD5\u7279\u65482 (1)"
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 2250557162536207891}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 1.2666666666666666
    m_ClipIn: 0
    m_Asset: {fileID: 7835311218715804966}
    m_Duration: 1.3362465836728612
    m_TimeScale: 1
    m_ParentTrack: {fileID: 8293633450796141333}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: -1
    m_BlendOutDuration: -1
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: "\u5DE8\u529B\u5C04\u51FB\u65BD\u6CD5\u7279\u6548"
  m_Markers:
    m_Objects: []
--- !u!114 &8336489461035930840
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0bf0785297c514c44b7678a48519eb23, type: 3}
  m_Name: "\u65BD\u6CD5\u7279\u65482 (2)"
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 1
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 2250557162536207891}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 1.2666666666666666
    m_ClipIn: 0
    m_Asset: {fileID: -6729283777437973898}
    m_Duration: 5
    m_TimeScale: 1
    m_ParentTrack: {fileID: 8336489461035930840}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: -1
    m_BlendOutDuration: -1
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: "\u5DE8\u529B\u5C04\u51FBtest"
  m_Markers:
    m_Objects: []
--- !u!114 &8364546783546903734
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0fc6f5187a81dc47999eefade6f0935, type: 3}
  m_Name: "\u76EE\u6807"
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 11400000}
  m_Children:
  - {fileID: 3924848909126453453}
  - {fileID: 4243493128814240694}
  m_Clips: []
  m_Markers:
    m_Objects: []
--- !u!114 &8539448655968932695
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8abdae81ccd3ebf43bb7a0790c551176, type: 3}
  m_Name: BattlePlayableAsset_PlaySE(Clone)(Clone)(Clone)(Clone)(Clone)(Clone)
  m_EditorClassIdentifier: 
  sourceGameObject:
    exposedName: 67f5f96de0fba4346a226748b4f810d4
    defaultValue: {fileID: 0}
  toPlaySE: 1
  seKey: "\u6218\u6597_\u5C04\u5F132"
  bgmKey: 
