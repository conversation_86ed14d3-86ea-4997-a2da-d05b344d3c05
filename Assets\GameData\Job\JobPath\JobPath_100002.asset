%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c22d0943bcd76f642b1369b0d7e94125, type: 3}
  m_Name: JobPath_100002
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes: []
  jobPath_SO: {fileID: 11400000}
  jobPath:
    Icon: {fileID: 21300000, guid: 7cacd8df3273e654ebdd668df43e477b, type: 3}
    jobPath: "\u5192\u9669\u8005"
    weightCapacity: 0
    jobPathUnlock: 1
    STR: 3
    STA: 3
    DEX: 3
    INT: 2
    FAITH: 2
    initialWeapon: {fileID: 11400000, guid: d255c1bed906f1d4783cde55754dc991, type: 2}
    initialMark: {fileID: 0}
    isInitialJob: 0
    initialCards:
    - card: {fileID: 11400000, guid: cce1c8fe61d68bb49acca26e413fa921, type: 2}
      count: 10
    - card: {fileID: 11400000, guid: c62abde34187ffc4383a41a891e5620f, type: 2}
      count: 2
    - card: {fileID: 11400000, guid: 13e1a29ae5c630a418e87a0c207e4238, type: 2}
      count: 3
    description: "\u70ED\u7231\u5192\u9669\uFF0C\u901A\u8FC7\u516C\u4F1A\u4EFB\u52A1\u3001\u5404\u5904\u7684\u60AC\u8D4F\u4EFB\u52A1\u4EE5\u53CA\u5BF9\u5404\u79CD\u5730\u57CE\u7684\u63A2\u7D22\uFF0C\u6765\u83B7\u53D6\u91D1\u94B1\u4E0E\u8D44\u6E90\u7528\u6765\u6B66\u88C5\u4EE5\u53CA\u517B\u6D3B\u81EA\u5DF1\u3002\n\n\u662F\u4E00\u4E2A\u5165\u95E8\u95E8\u69DB\u4F4E\uFF0C\u6CA1\u6709\u7279\u8272\uFF0C\u96BE\u4EE5\u6709\u672A\u6765\u7684\u804C\u4E1A\u3002"
    jobStages:
    - weaponType: 0000000005000000
      colorNum:
      - cardColor: 0
        count: 10
      - cardColor: 2
        count: 3
      - cardColor: 1
        count: 10
      - cardColor: 3
        count: 5
      - cardColor: 4
        count: 3
      - cardColor: 5
        count: 2
      minCardsCount: 15
      inherentCards:
      - card: {fileID: 11400000, guid: 2e7ac298ecc552647a927cc87061faf3, type: 2}
        count: 8
      - card: {fileID: 11400000, guid: a33b1c8b1ca43ab4e91b3157ded08098, type: 2}
        count: 1
      - card: {fileID: 11400000, guid: 7ef069baae2d7ab47ab9267d988a9348, type: 2}
        count: 1
      giveCards: []
      upgradeMat: []
      MarkCountMax: 1
