using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Sirenix.OdinInspector;

public class Event_SetActorMovementSound : Event_Base
{
    [LabelText("脚步声配置")]
    public FootstepSoundConfig footstepSoundConfig;
    
    public override void DoEvent()
    {
        InteractionActor.instance.actorMovement.footstepSoundConfig.SetFootstepSound(
            footstepSoundConfig.leftFootstepKey_Run,
            footstepSoundConfig.rightFootstepKey_Run,
            footstepSoundConfig.leftFootstepKey_Walk,
            footstepSoundConfig.rightFootstepKey_Walk);
    }
}

[System.Serializable]
public class FootstepSoundConfig
{
    [LabelText("跑步声（左）"), TitleGroup("跑步声音")]
    public string leftFootstepKey_Run = "";
    [LabelText("跑步声（右）")]
    public string rightFootstepKey_Run = "";
    [LabelText("走路声（左）"), TitleGroup("走路声音")]
    public string leftFootstepKey_Walk = "";
    [LabelText("走路声（右）")]
    public string rightFootstepKey_Walk = "";
    [LabelText("受限声（左）"), TitleGroup("受限声音")]
    public string leftFootstepKey_Constrain = "";
    [LabelText("受限声（右）")]
    public string rightFootstepKey_Constrain = "";
    
    public FootstepSoundConfig()
    {
        SetFootstepSound("跑步声（左）_默认",
            "跑步声（右）_默认",
            "走路声（左）_默认",
            "走路声（右）_默认");
    }
    
    public void SetFootstepSound(string leftFootstepKeyRun, string rightFootstepKeyRun, string leftFootstepKeyWalk, 
        string rightFootstepKeyWalk, string leftFootstepKeyConstrain = "", string rightFootstepKeyConstrain = "")
    {
        this.leftFootstepKey_Run = leftFootstepKeyRun;
        this.rightFootstepKey_Run = rightFootstepKeyRun;
        this.leftFootstepKey_Walk = leftFootstepKeyWalk;
        this.rightFootstepKey_Walk = rightFootstepKeyWalk;
        this.leftFootstepKey_Constrain = leftFootstepKeyConstrain;
        this.rightFootstepKey_Constrain = rightFootstepKeyConstrain;
    }

    private float GetRandomVolume() => Random.Range(InteractionActor.instance.actorMovement.minVolume / 100f, 
        InteractionActor.instance.actorMovement.maxVolume / 100f);
    
    public void LeftWalk() => AudioManager.Instance?.PlaySE(leftFootstepKey_Walk, modifier: GetRandomVolume());
    public void RightWalk() => AudioManager.Instance?.PlaySE(rightFootstepKey_Walk, modifier: GetRandomVolume());
    public void LeftRun() => AudioManager.Instance?.PlaySE(leftFootstepKey_Run, modifier: GetRandomVolume());
    public void RightRun() => AudioManager.Instance?.PlaySE(rightFootstepKey_Run, modifier: GetRandomVolume());
    public void LeftConstrain() => AudioManager.Instance?.PlaySE(leftFootstepKey_Constrain, modifier: GetRandomVolume());
    public void RightConstrain() => AudioManager.Instance?.PlaySE(rightFootstepKey_Constrain, modifier: GetRandomVolume());
}
