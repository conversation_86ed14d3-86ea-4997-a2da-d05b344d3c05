using Sirenix.OdinInspector;
using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;

public class PP_Gama : PP_Base
{
    internal static PP_Gama instance;

    private void Awake()
    {
        if (instance == null)
        {
            instance = this;
        }
        else
        {
            if (instance != this)
            {
                Destroy(gameObject);
            }
        }
    }

    [ShowInInspector]
    private LiftGammaGain liftGammaGain
    {
        get
        {
            LiftGammaGain lgg;
            volumeProfile.TryGet<LiftGammaGain>(out lgg);
            if (lgg == null)
            {
                // 如果当前 profile 中没有 LiftGammaGain，尝试添加一个
                lgg = volumeProfile.Add<LiftGammaGain>();
                lgg.active = true;
            }
            return lgg;
        }
    }

    [Title("Lift Gamma Gain 控制")]
    [ShowInInspector, LabelText("启用效果")]
    public bool IsActive
    {
        get => liftGammaGain?.active ?? false;
        set
        {
            if (liftGammaGain != null)
                liftGammaGain.active = value;
        }
    }

    [Title("Lift (阴影/暗部)")]
    [ShowInInspector, LabelText("Lift R (红色阴影)"), PropertyRange(0f, 2f)]
    public float LiftR
    {
        get => liftGammaGain?.lift.value.x ?? 1f;
        set
        {
            if (liftGammaGain != null)
            {
                var lift = liftGammaGain.lift.value;
                lift.x = value;
                liftGammaGain.lift.value = lift;
                liftGammaGain.lift.overrideState = true;
            }
        }
    }

    [ShowInInspector, LabelText("Lift G (绿色阴影)"), PropertyRange(0f, 2f)]
    public float LiftG
    {
        get => liftGammaGain?.lift.value.y ?? 1f;
        set
        {
            if (liftGammaGain != null)
            {
                var lift = liftGammaGain.lift.value;
                lift.y = value;
                liftGammaGain.lift.value = lift;
                liftGammaGain.lift.overrideState = true;
            }
        }
    }

    [ShowInInspector, LabelText("Lift B (蓝色阴影)"), PropertyRange(0f, 2f)]
    public float LiftB
    {
        get => liftGammaGain?.lift.value.z ?? 1f;
        set
        {
            if (liftGammaGain != null)
            {
                var lift = liftGammaGain.lift.value;
                lift.z = value;
                liftGammaGain.lift.value = lift;
                liftGammaGain.lift.overrideState = true;
            }
        }
    }

    [ShowInInspector, LabelText("Lift 强度"), PropertyRange(-1f, 1f)]
    public float LiftIntensity
    {
        get => liftGammaGain?.lift.value.w ?? 0f;
        set
        {
            if (liftGammaGain != null)
            {
                var lift = liftGammaGain.lift.value;
                lift.w = value;
                liftGammaGain.lift.value = lift;
                liftGammaGain.lift.overrideState = true;
            }
        }
    }

    [Title("Gamma (中间调)")]
    [ShowInInspector, LabelText("Gamma R (红色中间调)"), PropertyRange(0f, 2f)]
    public float GammaR
    {
        get => liftGammaGain?.gamma.value.x ?? 1f;
        set
        {
            if (liftGammaGain != null)
            {
                var gamma = liftGammaGain.gamma.value;
                gamma.x = value;
                liftGammaGain.gamma.value = gamma;
                liftGammaGain.gamma.overrideState = true;
            }
        }
    }

    [ShowInInspector, LabelText("Gamma G (绿色中间调)"), PropertyRange(0f, 2f)]
    public float GammaG
    {
        get => liftGammaGain?.gamma.value.y ?? 1f;
        set
        {
            if (liftGammaGain != null)
            {
                var gamma = liftGammaGain.gamma.value;
                gamma.y = value;
                liftGammaGain.gamma.value = gamma;
                liftGammaGain.gamma.overrideState = true;
            }
        }
    }

    [ShowInInspector, LabelText("Gamma B (蓝色中间调)"), PropertyRange(0f, 2f)]
    public float GammaB
    {
        get => liftGammaGain?.gamma.value.z ?? 1f;
        set
        {
            if (liftGammaGain != null)
            {
                var gamma = liftGammaGain.gamma.value;
                gamma.z = value;
                liftGammaGain.gamma.value = gamma;
                liftGammaGain.gamma.overrideState = true;
            }
        }
    }

    [ShowInInspector, LabelText("Gamma 强度"), PropertyRange(-1f, 1f)]
    public float GammaIntensity
    {
        get => liftGammaGain?.gamma.value.w ?? 0f;
        set
        {
            if (liftGammaGain != null)
            {
                var gamma = liftGammaGain.gamma.value;
                gamma.w = value;
                liftGammaGain.gamma.value = gamma;
                liftGammaGain.gamma.overrideState = true;
            }
        }
    }

    [Title("Gain (高光/亮部)")]
    [ShowInInspector, LabelText("Gain R (红色高光)"), PropertyRange(0f, 2f)]
    public float GainR
    {
        get => liftGammaGain?.gain.value.x ?? 1f;
        set
        {
            if (liftGammaGain != null)
            {
                var gain = liftGammaGain.gain.value;
                gain.x = value;
                liftGammaGain.gain.value = gain;
                liftGammaGain.gain.overrideState = true;
            }
        }
    }

    [ShowInInspector, LabelText("Gain G (绿色高光)"), PropertyRange(0f, 2f)]
    public float GainG
    {
        get => liftGammaGain?.gain.value.y ?? 1f;
        set
        {
            if (liftGammaGain != null)
            {
                var gain = liftGammaGain.gain.value;
                gain.y = value;
                liftGammaGain.gain.value = gain;
                liftGammaGain.gain.overrideState = true;
            }
        }
    }

    [ShowInInspector, LabelText("Gain B (蓝色高光)"), PropertyRange(0f, 2f)]
    public float GainB
    {
        get => liftGammaGain?.gain.value.z ?? 1f;
        set
        {
            if (liftGammaGain != null)
            {
                var gain = liftGammaGain.gain.value;
                gain.z = value;
                liftGammaGain.gain.value = gain;
                liftGammaGain.gain.overrideState = true;
            }
        }
    }

    [ShowInInspector, LabelText("Gain 强度"), PropertyRange(-1f, 1f)]
    public float GainIntensity
    {
        get => liftGammaGain?.gain.value.w ?? 0f;
        set
        {
            if (liftGammaGain != null)
            {
                var gain = liftGammaGain.gain.value;
                gain.w = value;
                liftGammaGain.gain.value = gain;
                liftGammaGain.gain.overrideState = true;
            }
        }
    }

    [Title("便捷控制方法")]
    [Button("重置所有参数")]
    public void ResetAll()
    {
        if (liftGammaGain != null)
        {
            liftGammaGain.lift.value = new Vector4(1f, 1f, 1f, 0f);
            liftGammaGain.gamma.value = new Vector4(1f, 1f, 1f, 0f);
            liftGammaGain.gain.value = new Vector4(1f, 1f, 1f, 0f);

            liftGammaGain.lift.overrideState = true;
            liftGammaGain.gamma.overrideState = true;
            liftGammaGain.gain.overrideState = true;
        }
    }

    [Button("设置整体 Gamma")]
    public void SetOverallGamma(float gammaValue)
    {
        if (liftGammaGain != null)
        {
            liftGammaGain.gamma.value = new Vector4(1, 1, 1, gammaValue);
            liftGammaGain.gamma.overrideState = true;
        }
    }

    [Button("设置整体 Gain")]
    public void SetOverallGain(float gainValue)
    {
        if (liftGammaGain != null)
        {
            liftGammaGain.gain.value = new Vector4(1, 1, 1, gainValue);
            liftGammaGain.gain.overrideState = true;
        }
    }

    [Button("设置整体 Lift")]
    public void SetOverallLift(float liftValue)
    {
        if (liftGammaGain != null)
        {
            liftGammaGain.lift.value = new Vector4(1, 1, 1, liftValue);
            liftGammaGain.lift.overrideState = true;
        }
    }

    /// <summary>
    /// 设置完整的 Lift Gamma Gain 参数
    /// </summary>
    /// <param name="lift">Lift 值 (阴影)</param>
    /// <param name="gamma">Gamma 值 (中间调)</param>
    /// <param name="gain">Gain 值 (高光)</param>
    public void SetLiftGammaGain(Vector4 lift, Vector4 gamma, Vector4 gain)
    {
        if (liftGammaGain != null)
        {
            liftGammaGain.lift.value = lift;
            liftGammaGain.gamma.value = gamma;
            liftGammaGain.gain.value = gain;

            liftGammaGain.lift.overrideState = true;
            liftGammaGain.gamma.overrideState = true;
            liftGammaGain.gain.overrideState = true;
        }
    }

    /// <summary>
    /// 获取当前的 Lift Gamma Gain 参数
    /// </summary>
    /// <returns>包含 lift, gamma, gain 的元组</returns>
    public (Vector4 lift, Vector4 gamma, Vector4 gain) GetLiftGammaGain()
    {
        if (liftGammaGain != null)
        {
            return (liftGammaGain.lift.value, liftGammaGain.gamma.value, liftGammaGain.gain.value);
        }
        return (Vector4.one, Vector4.one, Vector4.one);
    }
}
