using System;
using System.Linq;
using UnityEngine;
using DG.Tweening;
using UnityEngine.Events;
using Sirenix.OdinInspector;
using System.Collections.Generic;
using System.Threading;
using Cysharp.Threading.Tasks;
using Random = UnityEngine.Random;


public class AudioManager : MonoBehaviour
{
    public static AudioManager Instance;
    public AudioSource musicSource;
    public AudioSource circumstanceSource;
    public AudioSource soundEffectSource;
    public MusicConfig_SO musicConfig;
    public MusicConfig_SO circumstanceMusicConfig;
    public MusicConfig_SO seConfig;

    // AudioSource对象池相关
    [Header("AudioSource Pool Settings")]
    [SerializeField] private int maxPoolSize = 10;
    [SerializeField] private int initialPoolSize = 3;
    private Queue<AudioSource> audioSourcePool = new Queue<AudioSource>();
    private List<AudioSource> activeAudioSources = new List<AudioSource>();
    private Transform audioSourceParent;

    public string GetFormerMusicKey() => formerMusicConfig?.MusicKey;
    private MusicConfig formerMusicConfig;
    public string GetCurrentMusicKey() => currentMusicConfig?.MusicKey;
    private MusicConfig currentMusicConfig;

    public void SetBattleBGMName(string musicKeyName) => battleBGMName = musicKeyName;
    public void ResetBattleBGMName() => battleBGMName = BATTLE_BGM_NAME;
    private string battleBGMName = BATTLE_BGM_NAME;
    public const string BATTLE_BGM_NAME = "BattleBGM";

    [ShowInInspector] public int backgroundMusicVolume { get; private set; }
    public void SetBGMVolume(int value)
    {
        // Debug.LogError("设置背景音乐音量：" + value);
        backgroundMusicVolume = value;
    }
    [ShowInInspector] public int soundEffectVolume { get; private set; }
    public void SetSeVolume(int value) => soundEffectVolume = value;
    [ShowInInspector] public int circumstanceVolume { get; private set; }
    public void SetCircumVolume(int value) => circumstanceVolume = value;
    
    private Tween bgmFadeInTween;
    private Tween circumstanceFadeInTween;
    private Tween seFadeInTween;
    
    private Tween bgmFadeOutTween;
    private Tween circumstanceFadeOutTween;
    private Tween seFadeOutTween;
 
    void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else
        {
            Destroy(gameObject);
        }
    }

    public void Initialize()
    {
        musicSource.clip = null;
        soundEffectSource.clip = null;
        // 设置背景音乐音量和音效音量
        SetBackgroundMusicVolume(backgroundMusicVolume);
        SetSoundEffectVolume(soundEffectVolume);
        SetCircumstanceSourceVolume(circumstanceVolume);

        BattleEventManager.Instance.AddListener(BattleEventName.BATTLE_START, PlayBattleBGM);
        BattleEventManager.Instance.AddListener(BattleEventName.BATTLE_OVER, ResetBattleBGMName);
        
        CancelToken(_cancelBGMDelay);
        CancelToken(_cancelCircumCoolDown);

        _cancelBGMDelay = new CancellationTokenSource();
        _cancelCircumCoolDown = new CancellationTokenSource();
        
        // 初始化AudioSource对象池
        InitializeAudioSourcePool();
    }

    void Update()
    {
        // 更新Inspector显示的对象池状态
        PooledAudioSourceCount = audioSourcePool.Count;
        ActiveAudioSourceCount = activeAudioSources.Count;
    }

    void OnDestroy()
    {
        musicSource.clip = null;
        soundEffectSource.clip = null;
        
        // 清理AudioSource对象池
        StopAllPooledAudioSources();
        CancelToken(_cancelBGMDelay);
        CancelToken(_cancelCircumCoolDown);
    }
    
    [ReadOnly] public string CurrentMusicName;
    [ReadOnly] public string FormerMusicName;
    [ReadOnly] public string CurrentCircumstanceMusicName;
    [ReadOnly] public int PooledAudioSourceCount;
    [ReadOnly] public int ActiveAudioSourceCount;
    

    // [Button("开始播放音乐")]
    // private void TestMusic()
    // {
    //     CurrentMusicName = NextPlayMusicName;
    //     PlayMusic(CurrentMusicName, 1);
    // }
    //
    // [Button("切换下一个音乐")]
    // private void TestSwitchMusic()
    // {
    //     if (NextPlayMusicName == "" || NextPlayMusicName == null)
    //         return;
    //     
    //     if (CurrentMusicName == NextPlayMusicName)
    //         return;
    //
    //     CurrentMusicName = NextPlayMusicName;
    //     SwitchMusic(CurrentMusicName, 3);
    // }

    [Button("停止播放")]
    private void StopPlay()
    {
        StopMusic(0, false);
    }

    /// <summary> 播放音乐 </summary>
    public void PlayMusic(string musicKey, float transInTime = 1.5f, bool transIn = true, UnityAction onComplete = null)
    {
        // Debug.Log("开始播放音乐：" + musicKey);
        
        if (bgmFadeInTween != null && bgmFadeInTween.IsPlaying())
        {
            bgmFadeInTween.Kill();
            musicSource.Stop();
        }
        
        formerMusicConfig = currentMusicConfig;
        // string tempMusicName = formerMusicName;
        FormerMusicName = CurrentMusicName;
        
        AudioClip musicClip = GetAudioClipByMusicKey(musicKey, musicConfig, out currentMusicConfig);
        if (!musicClip)
            return;
        
        CurrentMusicName = currentMusicConfig.MusicKey;
        musicSource.clip = musicClip;
        musicSource.loop = true;
        int realVolumeValue = Mathf.RoundToInt(currentMusicConfig.Volume * backgroundMusicVolume/ 100f);
        
        if (transIn)
        {
            SetBackgroundMusicVolume(0);
            musicSource.Play();
            bgmFadeInTween = musicSource.DOFade(realVolumeValue / 100f, transInTime);
        }
        else
        {
            SetBackgroundMusicVolume(realVolumeValue);
            musicSource.Play();
        }
    }

    /// <summary> 播放环境音效 </summary>
    public void PlayCircumstanceMusic(string musicKey, float transInTime = 1.5f, bool transIn = true, UnityAction onComplete = null)
    {
        if (circumstanceFadeInTween != null && circumstanceFadeInTween.IsPlaying())
        {
            circumstanceFadeInTween.Kill();
            circumstanceSource.Stop();
        }

        AudioClip musicClip = GetAudioClipByMusicKey(musicKey, circumstanceMusicConfig, out var currentCircumstanceMusicConfig);
        if (musicClip == null)
            return;
        
        CurrentCircumstanceMusicName = currentCircumstanceMusicConfig.MusicKey;
        circumstanceSource.clip = musicClip;
        circumstanceSource.loop = true;
        int realVolumeValue = Mathf.RoundToInt(currentCircumstanceMusicConfig.Volume * circumstanceVolume/ 100f);
        
        
        if (transIn)
        {
            SetCircumstanceSourceVolume(0);
            circumstanceSource.Play();
            circumstanceFadeInTween = circumstanceSource.DOFade(realVolumeValue/ 100f, transInTime);
        }
        else
        {
            SetCircumstanceSourceVolume(realVolumeValue);
            circumstanceSource.Play();
        }
    }

    public void StopCircumstanceMusic(float transOutTime = 1.5f, bool transOut = true, UnityAction onComplete = null)
    {
        if (circumstanceFadeOutTween != null && circumstanceFadeOutTween.IsPlaying())
        {
            circumstanceFadeOutTween.Kill();
            circumstanceSource.Stop();
        }
        
        if (transOut)
        {
            circumstanceFadeOutTween = circumstanceSource.DOFade(0.0f, transOutTime).OnComplete(() => {
                circumstanceSource.Stop();
                circumstanceSource.clip = null;
                onComplete?.Invoke();
            });
        }
        else
        {
            circumstanceSource.Stop();
            circumstanceSource.clip = null;
            onComplete?.Invoke();
        }
    }

    public bool IsSePlaying() => soundEffectSource.isPlaying;

    /// <summary> 播放SE </summary>
    public void PlaySE(string seKey, bool isStopBGM = false, float transInTime = 0f, bool transIn = false, 
        UnityAction onComplete = null, bool isLoop = false, float modifier = 0.0f)
    {
        if (seFadeInTween != null && seFadeInTween.IsPlaying())
        {
            seFadeInTween.Kill();
            soundEffectSource.Stop();
        }
        
        AudioClip seClip = GetAudioClipByMusicKey(seKey, seConfig, out var currentSeConfig);
        if (seClip == null)
            return;

        if (isStopBGM)
            StopMusic(0.3f);
        
        int realVolumeValue = Mathf.RoundToInt(currentSeConfig.Volume * soundEffectVolume/ 100f);
        
        if (transIn)
        {
            SetSoundEffectVolume(0);
            if (isLoop)
            {
                soundEffectSource.loop = true;
                soundEffectSource.clip = seClip;
                soundEffectSource.Play();
            }
            else
                soundEffectSource.PlayOneShot(seClip, realVolumeValue / 100f * (1 + modifier));
            seFadeInTween = soundEffectSource.DOFade(realVolumeValue / 100f, transInTime);
        }
        else
        {
            SetSoundEffectVolume(realVolumeValue);
            if (isLoop)
            {
                soundEffectSource.loop = true;
                soundEffectSource.clip = seClip;
                soundEffectSource.Play();
            }
            soundEffectSource.PlayOneShot(seClip, realVolumeValue / 100f * (1 + modifier));
        }
    }

    /// <summary> 停止背景音乐 </summary>
    public void StopMusic(float transOutTime = 1.5f, bool transOut = true, UnityAction onComplete = null)
    {
        if (bgmFadeOutTween != null && bgmFadeOutTween.IsPlaying())
        {
            bgmFadeOutTween.Kill();
            musicSource.Stop();
        }
        
        if (transOut)
        {
            bgmFadeOutTween = musicSource.DOFade(0.0f, transOutTime).OnComplete(() => {
                musicSource.Stop();
                onComplete?.Invoke();
            });
        }
        else
        {
            musicSource.Stop();
            onComplete?.Invoke();
        }
    }

    /// <summary> 停止SE </summary>
    public void StopSE(float transOutTime = 0.5f, bool transOut = true, UnityAction onComplete = null)
    {
        if (seFadeOutTween != null && seFadeOutTween.IsPlaying())
        {
            seFadeOutTween.Kill();
            soundEffectSource.Stop();
        }
        
        if (transOut)
        {
            seFadeOutTween = soundEffectSource.DOFade(0.0f, transOutTime).OnComplete(() => {
                soundEffectSource.Stop();
                onComplete?.Invoke();
            });
        }
        else
        {
            soundEffectSource.Stop();
            onComplete?.Invoke();
        }
    }

    /// <summary> 开始过渡播放 </summary>
    public void SwitchMusic(string switchToMusicKey, float transInTime = 1.5f, float transOutTime = 1.5f,
        float delayTime = 0f, bool continuePlayingCurrent = false)
    {
        // Debug.Log($"开始切换BGM。延迟时长：{delayTime}");
        // TODO:只要产生了BGM的切换，立即停止播放氛围音效
        // StopCircumstanceMusic();
        
        if (CurrentMusicName == switchToMusicKey || switchToMusicKey == "")
            return;
            
        // 淡出第一段音乐；结束后开始播放第二段音乐
        StopMusic(transOutTime, true, async () =>
        {
            // Debug.Log($"开始延迟播放BGM。延迟时长：{delayTime}");
            CancelToken(_cancelBGMDelay);
            _cancelBGMDelay = new CancellationTokenSource();
            var bgmDelay = await MusicStartDelay(_cancelBGMDelay.Token, delayTime, () =>
            {
                PlayMusic(switchToMusicKey, transInTime, true);
            }).SuppressCancellationThrow();

            if (!bgmDelay)
                return;
            
            // Debug.Log("BGM延迟播放中断");
            
            if (continuePlayingCurrent)
                PlayMusic(switchToMusicKey, transInTime, true);
        });
    }
    
    /// <summary> 开始过渡播放 </summary>
    public async void SwitchCircumstance(string switchToCircumstanceKey, float transInTime = 1.5f, 
        float transOutTime = 1.5f, float delayTime = 0f, bool continuePlayingCurrent = false)
    {
        CancelToken(_cancelCircumCoolDown);
        _cancelCircumCoolDown = new CancellationTokenSource();
        var circumDelay = await MusicStartDelay(_cancelCircumCoolDown.Token, delayTime, () => {
            if (CurrentCircumstanceMusicName == switchToCircumstanceKey || switchToCircumstanceKey == "")
                return;
            
            // Debug.Log("开始播放环境音效：" + switchToCircumstanceKey);
            
            // 淡出第一段音乐；结束后开始播放第二段音乐
            StopCircumstanceMusic(transOutTime, true, () => {
                PlayCircumstanceMusic(switchToCircumstanceKey, transInTime, true);
            });
        }).SuppressCancellationThrow();
        
        if (circumDelay)
        {
            // Debug.Log("环境音效延迟播放中断");
            
            if (CurrentCircumstanceMusicName == switchToCircumstanceKey || switchToCircumstanceKey == "")
                return;
            
            if (continuePlayingCurrent)
            {
                // Debug.Log("开始播放环境音效：" + switchToCircumstanceKey);
                // 淡出第一段音乐；结束后开始播放第二段音乐
                StopCircumstanceMusic(transOutTime, true,
                    () => { PlayCircumstanceMusic(switchToCircumstanceKey, transInTime, true); });
            }
        }
    }
    
    /// <summary> 设置BGM音量 </summary>
    public void SetBackgroundMusicVolume(int volume)
    {
        // Debug.Log($"设置背景音乐音量：{volume}");
        musicSource.volume = volume / 100f;
    }
    
    /// <summary> 设置环境音量 </summary>
    public void SetCircumstanceSourceVolume(int volume) => circumstanceSource.volume = volume / 100f;
 
    /// <summary> 设置SE音量 </summary>
    public void SetSoundEffectVolume(int volume) => soundEffectSource.volume = volume / 100f;
    
    /// <summary>
    /// 根据给定音频Key获取AudioClip
    /// </summary>
    /// <param name="musicKey"></param>
    /// <returns></returns>
    public AudioClip GetAudioClipByMusicKey(string musicKey, MusicConfig_SO configSO, out MusicConfig getMusicConfig)
    {
        AudioClip musicClip;
        List<MusicConfig> tempConfigs = configSO.MusicSourceList.FindAll(config => config.MusicKey == musicKey);
        if (tempConfigs.Count <= 0)
        {
            Debug.LogError($"没有找到名为{musicKey}音频Key！请检查！");
            getMusicConfig = null;
        }
        else if (tempConfigs.Count == 1)
            getMusicConfig = tempConfigs[0];
        else
        {
            int randomIndex = Random.Range(0, tempConfigs.Count);
            getMusicConfig = tempConfigs[randomIndex];
        }
        
        if (getMusicConfig != null)
        {
            musicClip = getMusicConfig.MusicClipSource;
            if (musicClip == null)
            {
                // Debug.LogError($"{musicKey}音频Key没有对应的音频文件！请检查！");
                return null;
            }
            else
                return musicClip;
        }
        else
            return null;
    }

    public void PlaySingleSE(string seKey)
    {
        PlaySE(seKey);
    }

    public void PlayBGM(string bgmKey, float transInTime = 1.5f, float transOutTime = 1.5f, float delayTime = 0f, 
        bool continuePlayingBGM = false)
    {
        // Debug.Log($"尝试播放背景音乐：{bgmKey}");
        SwitchMusic(bgmKey, transInTime, transOutTime, delayTime, continuePlayingBGM);
    }

    public void PlayBattleBGM()
    {
        float delayTime = 0f;
        SwitchMusic(battleBGMName, 1, delayTime:delayTime);
    }

    public void ExitBattleBGM()
    {
        float delayTime = 0f;
        SwitchMusic(FormerMusicName, 1, delayTime:delayTime);
    }

    public void  SetNullBGM()
    {
        musicSource.clip = null;
    }

    public  void SetNullCircumstance()
    {
        circumstanceSource.clip = null;
    }
    
    private CancellationTokenSource _cancelBGMDelay;
    private CancellationTokenSource _cancelCircumCoolDown;
    private void CancelToken(CancellationTokenSource cancelToken)
    {
        cancelToken?.Cancel();
        cancelToken?.Dispose();
    }
    
    private async UniTask MusicStartDelay(CancellationToken cancellationToken, float delayTime, Action onComplete = null)
    {
        float timer = 0f;
        while (timer < delayTime)
        {
            timer += Time.deltaTime;
            await UniTask.NextFrame(cancellationToken);
        }
        onComplete?.Invoke();
    }

    private void InitializeAudioSourcePool()
    {
        // 创建AudioSource父对象
        GameObject audioSourceParentGO = new GameObject("AudioSource Pool");
        audioSourceParentGO.transform.SetParent(transform);
        audioSourceParent = audioSourceParentGO.transform;
        
        // 预创建初始数量的AudioSource
        for (int i = 0; i < initialPoolSize; i++)
        {
            CreateNewAudioSource();
        }
    }

    /// <summary>
    /// 创建新的AudioSource
    /// </summary>
    private AudioSource CreateNewAudioSource()
    {
        GameObject audioSourceGO = new GameObject($"PooledAudioSource_{audioSourcePool.Count}");
        audioSourceGO.transform.SetParent(audioSourceParent);
        
        AudioSource audioSource = audioSourceGO.AddComponent<AudioSource>();
        audioSource.playOnAwake = false;
        audioSource.loop = false;
        
        audioSourcePool.Enqueue(audioSource);
        return audioSource;
    }
    
    /// <summary>
    /// 从对象池获取AudioSource
    /// </summary>
    private AudioSource GetAudioSourceFromPool()
    {
        AudioSource audioSource;
        
        if (audioSourcePool.Count > 0)
        {
            audioSource = audioSourcePool.Dequeue();
        }
        else if (activeAudioSources.Count < maxPoolSize)
        {
            // 如果池为空但未达到最大数量，创建新的
            audioSource = CreateNewAudioSource();
            audioSourcePool.Dequeue(); // 移除刚加入的
        }
        else
        {
            // 如果达到最大数量，复用最早的AudioSource
            audioSource = activeAudioSources[0];
            activeAudioSources.RemoveAt(0);
            audioSource.Stop();
        }
        
        activeAudioSources.Add(audioSource);
        return audioSource;
    }
    
    /// <summary>
    /// 将AudioSource回收到对象池
    /// </summary>
    private void ReturnAudioSourceToPool(AudioSource audioSource)
    {
        if (audioSource == null) return;
        
        activeAudioSources.Remove(audioSource);
        audioSource.Stop();
        audioSource.clip = null;
        audioSource.loop = false;
        audioSource.volume = 1f;
        audioSourcePool.Enqueue(audioSource);
    }
    
    /// <summary>
    /// 使用新的AudioSource播放SE
    /// </summary>
    public void PlaySEWithNewAS(string seKey, bool isStopBGM = false, float transInTime = 0f, bool transIn = false, UnityAction onComplete = null, bool isLoop = false)
    {
        AudioClip seClip = GetAudioClipByMusicKey(seKey, seConfig, out var currentSeConfig);
        if (seClip == null)
            return;

        if (isStopBGM)
            StopMusic(0.3f);
        
        AudioSource pooledAudioSource = GetAudioSourceFromPool();
        int realVolumeValue = Mathf.RoundToInt(currentSeConfig.Volume * soundEffectVolume / 100f);
        
        pooledAudioSource.clip = seClip;
        pooledAudioSource.loop = isLoop;
        
        if (transIn)
        {
            pooledAudioSource.volume = 0f;
            pooledAudioSource.Play();
            
            pooledAudioSource.DOFade(realVolumeValue / 100f, transInTime).OnComplete(() =>
            {
                onComplete?.Invoke();
                if (!isLoop)
                {
                    // 如果不是循环播放，等待播放完成后回收
                    StartCoroutine(WaitForAudioComplete(pooledAudioSource, seClip.length));
                }
            });
        }
        else
        {
            pooledAudioSource.volume = realVolumeValue / 100f;
            pooledAudioSource.Play();
            onComplete?.Invoke();
            
            if (!isLoop)
            {
                // 如果不是循环播放，等待播放完成后回收
                StartCoroutine(WaitForAudioComplete(pooledAudioSource, seClip.length));
            }
        }
    }
    
    /// <summary>
    /// 等待音频播放完成后回收AudioSource
    /// </summary>
    private System.Collections.IEnumerator WaitForAudioComplete(AudioSource audioSource, float duration)
    {
        yield return new WaitForSeconds(duration);
        ReturnAudioSourceToPool(audioSource);
    }
    
    /// <summary>
    /// 停止所有池化的AudioSource
    /// </summary>
    public void StopAllPooledAudioSources()
    {
        for (int i = activeAudioSources.Count - 1; i >= 0; i--)
        {
            ReturnAudioSourceToPool(activeAudioSources[i]);
        }
    }
    
    /// <summary>
    /// 停止指定的池化AudioSource（通过音频Key）
    /// </summary>
    public void StopPooledAudioSourceByKey(string seKey)
    {
        AudioClip targetClip = GetAudioClipByMusicKey(seKey, seConfig, out var config);
        if (targetClip == null) return;
        
        for (int i = activeAudioSources.Count - 1; i >= 0; i--)
        {
            if (activeAudioSources[i].clip == targetClip)
            {
                ReturnAudioSourceToPool(activeAudioSources[i]);
            }
        }
    }
}

public interface IActiveSceneWithMusic
{
    void OnSceneActive();
    void OnSceneInactive();
}
