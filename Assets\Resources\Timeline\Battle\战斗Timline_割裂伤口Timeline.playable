%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-9131150055810968081
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8193a6916505c16468554e2c0ef6b1fe, type: 3}
  m_Name: BattlePlayableAsset_Control
  m_EditorClassIdentifier: 
  position: {x: 0, y: 0, z: 0}
  scale: {x: 1, y: 1, z: 1}
  rotation: {x: 0, y: 0, z: 0}
  changeRotation: 0
  effectPoint: {x: 0, y: 0, z: 0}
  sourceGameObject:
    exposedName: 877d45bfe80bf1e4abc6eb6f12f6b0f5
    defaultValue: {fileID: 0}
  prefabGameObject: {fileID: 7306781270364105029, guid: 244ff3d05c426fb47baf245222fc9eba,
    type: 3}
  updateParticle: 1
  particleRandomSeed: 9164
  updateDirector: 1
  updateITimeControl: 1
  searchHierarchy: 0
  active: 1
  postPlayback: 2
--- !u!114 &-6727078494650106262
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fee833bea217ac74894dad39ccfe5c0f, type: 3}
  m_Name: "\u97F3\u6548"
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 2250557162536207891}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 0.13333333333333333
    m_ClipIn: 0
    m_Asset: {fileID: 484456184863519347}
    m_Duration: 0.33333333333333337
    m_TimeScale: 1
    m_ParentTrack: {fileID: -6727078494650106262}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: -1
    m_BlendOutDuration: -1
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: "\u5229\u5203\u653B\u51FB2\u5200\u5251\u65A7"
  - m_Version: 1
    m_Start: 0.8166666666666667
    m_ClipIn: 0
    m_Asset: {fileID: 8083825093335285488}
    m_Duration: 0.3500000000000001
    m_TimeScale: 1
    m_ParentTrack: {fileID: -6727078494650106262}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: -1
    m_BlendOutDuration: -1
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: "\u5229\u5203\u653B\u51FB2\u5200\u5251\u65A7"
  m_Markers:
    m_Objects: []
--- !u!114 &-2330581402067055688
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0bf0785297c514c44b7678a48519eb23, type: 3}
  m_Name: "\u65BD\u6CD5\u7279\u65482 (1)"
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 2250557162536207891}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 0
    m_ClipIn: 0
    m_Asset: {fileID: -9131150055810968081}
    m_Duration: 2
    m_TimeScale: 1
    m_ParentTrack: {fileID: -2330581402067055688}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: -1
    m_BlendOutDuration: -1
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: "\u5272\u88C2\u4F24\u53E3\u65BD\u6CD5\u7279\u6548"
  m_Markers:
    m_Objects: []
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: bfda56da833e2384a9677cd3c976a436, type: 3}
  m_Name: "\u6218\u6597Timline_\u5272\u88C2\u4F24\u53E3Timeline"
  m_EditorClassIdentifier: 
  m_Version: 0
  m_Tracks:
  - {fileID: 2250557162536207891}
  m_FixedDuration: 0
  m_EditorSettings:
    m_Framerate: 60
    m_ScenePreview: 1
  m_DurationMode: 0
  m_MarkerTrack: {fileID: 0}
--- !u!114 &197468181708446978
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a11da72315417c54781b97c97fc03a87, type: 3}
  m_Name: BattlePlayableAsset_SpineAnimation
  m_EditorClassIdentifier: 
  sourceGameObject:
    exposedName: 1932c16824c8b89449b831f6795e3614
    defaultValue: {fileID: 0}
  spineAnimationState:
    behaviorTargetPrefab:
      exposedName: 
      defaultValue: {fileID: 0}
    position: {x: 0, y: 0, z: 0}
    scale: {x: 0, y: 0, z: 0}
    rotation: {x: 0, y: 0, z: 0}
    unit: {fileID: 0}
    skeletonAnimation: {fileID: 0}
    spineAnimTrueName: 
    formerPoseName: 
    spinePlaySpeed: 1
  animationState:
    behaviorTargetPrefab:
      exposedName: 
      defaultValue: {fileID: 0}
    position: {x: 0, y: 0, z: 0}
    scale: {x: 0, y: 0, z: 0}
    rotation: {x: 0, y: 0, z: 0}
    unit: {fileID: 0}
    skeletonAnimation: {fileID: 0}
    spineAnimTrueName: One_Atk4
    formerPoseName: One_Idle2
    spinePlaySpeed: 1
  spineAniName: "\u653B\u51FB4"
  weaponTypeName: "\u51B3\u6597\u5251"
  useCustomWeapon: 0
  spinePlaySpeed: 1
--- !u!114 &484456184863519347
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8abdae81ccd3ebf43bb7a0790c551176, type: 3}
  m_Name: BattlePlayableAsset_PlaySE(Clone)(Clone)
  m_EditorClassIdentifier: 
  sourceGameObject:
    exposedName: cadee66da9f7e0b4cad7ebefe520eab1
    defaultValue: {fileID: 0}
  toPlaySE: 1
  seKey: "\u6218\u6597_\u5229\u52031"
  bgmKey: 
--- !u!114 &2250557162536207891
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0fc6f5187a81dc47999eefade6f0935, type: 3}
  m_Name: "\u65BD\u6CD5\u8005"
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 11400000}
  m_Children:
  - {fileID: 5622222551031462675}
  - {fileID: -2330581402067055688}
  - {fileID: -6727078494650106262}
  m_Clips: []
  m_Markers:
    m_Objects: []
--- !u!114 &5622222551031462675
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 53462d5107f1f4e48b0da2816d3c6136, type: 3}
  m_Name: "\u65BD\u6CD5\u52A8\u4F5C1"
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 2250557162536207891}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 0
    m_ClipIn: 0
    m_Asset: {fileID: 197468181708446978}
    m_Duration: 1.6000001430511475
    m_TimeScale: 1
    m_ParentTrack: {fileID: 5622222551031462675}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: -1
    m_BlendOutDuration: -1
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: BattlePlayableAsset_SpineAnimation
  m_Markers:
    m_Objects: []
  trackIndex: 0
  unscaledTime: 0
--- !u!114 &8083825093335285488
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8abdae81ccd3ebf43bb7a0790c551176, type: 3}
  m_Name: BattlePlayableAsset_PlaySE(Clone)(Clone)(Clone)
  m_EditorClassIdentifier: 
  sourceGameObject:
    exposedName: 2afa2c88d5c4b6649a0c8874c7502243
    defaultValue: {fileID: 0}
  toPlaySE: 1
  seKey: "\u9B54\u6CD5_\u90AA\u60761"
  bgmKey: 
