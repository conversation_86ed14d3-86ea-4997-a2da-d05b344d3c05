using UnityEngine;
using UnityEngine.UI;
using TMPro;

public class SaveSlot : MonoBehaviour
{
    public int index;
    public Text playerName;
    public TextMeshProUGUI tmpPlayerName;
    public Text playerLevel;
    public TextMeshProUGUI tmpPlayerLevel;
    public Text playTime;
    public TextMeshProUGUI tmpPlayTime;
    public GameObject activeImage;
    public SaveSlotListActive saveSlotListActive;
    public NewCountLogin newCountLogin;

    bool SaveDataExist;
    void Start()
    {
        OnStart();
    }

    public void OnStart()
    {
        string path = string.Format("SaveData/save0{0}.save", index);
        if (SaveDataManager.instance.SaveDataExist(path))
        {
            transform.GetChild(1).gameObject.SetActive(false);
            transform.GetChild(2).gameObject.SetActive(true);
            MainActorData_SO mainActorData = ES3.Load<MainActorData_SO>("MainActorData_SO", path);

            SetText(playerName, tmpPlayerName, mainActorData.playerName);
            SetText(playerLevel, tmpPlayerLevel, mainActorData.playerLevel.ToString());
            SetText(playTime, tmpPlayTime, SaveDataManager.FormatTimeFromSeconds(mainActorData.playTime));
            SaveDataExist=true;
        }
        else
        {
            transform.GetChild(1).gameObject.SetActive(true);
            transform.GetChild(2).gameObject.SetActive(false);
            SaveDataExist=false;
        }
    }
    
    /// <summary>
    /// 设置文本内容，同时处理Text和TMP组件
    /// </summary>
    private void SetText(Text textComponent, TextMeshProUGUI tmpComponent, string content)
    {
        if (textComponent != null)
            textComponent.text = content;
        
        if (tmpComponent != null)
            tmpComponent.text = content;
    }
    
    /// <summary>
    /// 注册存档index，生成存档路径
    /// </summary>
    public void OnClick()
    {
        SaveDataManager.instance.SetSavePath(index);
        newCountLogin.创建新号();
    }

    /// <summary>
    /// 注册存档index，生成存档路径
    /// </summary>
    public void CreateData()
    {
        SaveDataManager.instance.SetSavePath(index);
    }

    /// <summary>
    /// 根据index读取存档
    /// </summary>
    public void ClickToLogin()
    {
        SaveDataManager.instance.Login(index);
    }

    public void mouseMoveOn()
    {
        saveSlotListActive.ActiveSlotLightMark(index);
    }
    
    public void ShowActiveMark()
    {
        activeImage.SetActive(true);
    }

    public void HideActiveMark()
    {
        activeImage.SetActive(false);
    }

    public void EnterAuto()
    {
        if (SaveDataExist)
        {
            ClickToLogin();
        }
        else
        {
            CreateData();
        }
    }

    public void DeleteSaveData()
    {
        var canvas = GetComponentInParent<CanvasGroup>();
        var doubleCheckWindow = UIManager.instance.GetDoubleCheckWindow(canvas.transform);
        string attentionText = LocalizationManager.Instance.GetLocalizedString("删档警告");
        doubleCheckWindow.Initialize(attentionText, () =>
            {
                SaveDataManager.instance.DeleteSaveData(index);
                OnStart();
            }
        );
    }
}
