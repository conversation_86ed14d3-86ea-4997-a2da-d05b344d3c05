using System.IO;
using System.Reflection;
using UnityEngine;
using PixelCrushers.DialogueSystem;
using DG.Tweening;
using System.Collections.Generic;
using Sirenix.OdinInspector;
using System;
using System.Collections;
using System.Threading;
using System.Threading.Tasks;
using UnityEngine.Events;
using UnityEngine.SceneManagement;

public class SaveDataManager : MonoBehaviour
{
    public static SaveDataManager instance;
    public CanvasGroup startPanel;
    public int SaveSlot;
    public string SavePath;
    public Vector3 bornPositon;
    public static bool forceQuit = false;

    // 游戏时长相关变量
    [Header("游戏时长管理")]
    [SerializeField] private float currentSessionPlayTime = 0f; // 当前游戏会话的游戏时长
    private float totalPlayTime = 0f; // 总游戏时长（包含之前存档的时长）

    private void Awake()
    {
        if (instance == null)
        {
            instance = this;
        }
        else
        {
            if (instance != this)
            {
                Destroy(gameObject);
            }
        }
        DontDestroyOnLoad(gameObject);
        Application.wantsToQuit += OnWantToQuit; // 添加退出事件监听
    }

    private bool OnWantToQuit()
    {
        if (forceQuit) return true; // 添加强制退出判断
        
        if (isSaving)
        {
            #if UNITY_EDITOR
                UnityEditor.EditorUtility.DisplayDialog("提示", "正在保存游戏数据，请稍候...", "确定");
            #else
                UIManager.Attention("正在保存游戏数据，请稍候...");
            #endif
            return false; // 返回 false 来阻止退出
        }
        
        if (GameManager.instance.systemProcess == GameManager.SystemProcess.game && 
            InteractionActor.instance.playerState.State != PlayerState.PlayerCurrentState.battle)
        {
            SaveAllActorData();
        }
        return true;
    }

    void OnDestroy()
    {
        Application.wantsToQuit -= OnWantToQuit; // 移除事件监听
    }

    /// <summary>
    /// 更新存档储存的位置（根据存档ID）
    /// </summary>
    /// <param name="slotID"></param>
    public void SetSavePath(int slotID)
    {
        SaveSlot = slotID;
        SavePath = string.Format("SaveData/save0{0}.save", slotID);
        ES3Settings.defaultSettings.path = SavePath;
        Debug.Log(ES3Settings.defaultSettings.path);
    }


    private bool isSaving = false;
    private float saveTimer = 0f;
    private const float SAVE_COOLDOWN = 2f;
    
    private void Update()
    {
        if (isSaving)
        {
            saveTimer -= Time.deltaTime;
            if (saveTimer <= 0)
            {
                isSaving = false;
            }
        }

        // 更新游戏时长
        if (GameManager.instance.systemProcess == GameManager.SystemProcess.game)
        {
            currentSessionPlayTime += Time.deltaTime;
        }
    }
    /// <summary>
    /// 使用重试机制安全地保存数据
    /// </summary>
    /// <param name="key">存储键名</param>
    /// <param name="value">要存储的值</param>
    /// <param name="path">可选的存储路径</param>
    /// <param name="maxRetries">最大重试次数</param>
    /// <param name="retryDelayMs">重试间隔(毫秒)</param>
    public void SafeSave<T>(string key, T value, string path = null, int maxRetries = 3, int retryDelayMs = 100)
    {
        for (int attempt = 0; attempt < maxRetries; attempt++)
        {
            try
            {
                if (string.IsNullOrEmpty(path))
                {
                    ES3.Save(key, value);
                }
                else
                {
                    ES3.Save(key, value, path);
                }
                return; // 保存成功，直接返回
            }
            catch (IOException ex)
            {
                if (attempt == maxRetries - 1)
                {
                    Debug.LogError($"保存数据失败，键名：{key}，已重试{maxRetries}次: {ex.Message}");
                    throw; // 最后一次尝试仍失败，抛出异常
                }
                
                Debug.LogWarning($"保存数据时出现文件访问冲突，键名：{key}，正在重试 ({attempt+1}/{maxRetries}): {ex.Message}");
                
                // 等待一段时间后重试
                Thread.Sleep(retryDelayMs);
            }
            catch (Exception ex)
            {
                Debug.LogError($"保存数据时出现其他异常，键名：{key}: {ex.Message}");
                throw; // 其他类型的异常直接抛出
            }
        }
    }

    /// <summary>
    /// 存储玩家所有数据
    /// </summary>
    [Button("存储玩家所有数据")]
    public void SaveAllActorData()
    {
        isSaving = true;
        saveTimer = SAVE_COOLDOWN;
        SavingFlag.instance.ShowSavingFlag();

        try
        {
            // 更新游戏时长到MainActorData
            UpdatePlayTimeToMainActorData();

            //存储玩家位置数据
            SafeSave("playerPosition", InteractionActor.instance.transform.position);
            
            //存储SO数据
            BattleAttributes battleAttri = MainActorManager.instance.UnitBattleAttributeMappingReverse();
            MainActorManager.instance.mainActorData_SO.battleAttributes = battleAttri;

            //存储初始卡数据
            List<(int,int)> freeCardsData = new List<(int, int)>();
            foreach (var card in MainActorManager.instance.mainActorData_SO.freeDeck)
            {
                freeCardsData.Add((card.cardData.cardDetails.cardID, card.cardCount));
            }

            SafeSave("playerFreeDeck", freeCardsData);
            SaveSOData(MainActorManager.instance.mainActorData_SO);
            SafeSave("playerWeapon", MainActorManager.instance.weaponData_SO.weaponDetail.weaponID);
            SafeSave("playerHelmet", MainActorManager.instance.helmetData_SO?.ID ?? -1);
            SafeSave("playerArmor", MainActorManager.instance.armorData_SO?.ID ?? -1);
            SafeSave<Dictionary<string, int>>("JobPathRank", JobDictionary.instance.JobPathRank);
            
            // 其他对象的保存操作
            MainActorManager.instance.mainActorData_SO.SaveAllData();
            MainActorManager.instance.inventoryCardData_SO.SaveAllData();
            MainActorManager.instance.InventoryWeaponData_SO.SaveData();
            MainActorManager.instance.InventoryItemData_SO.SaveAllData();
            MainActorManager.instance.inventoryEquipmentmentData_SO.SaveData();
            MainActorManager.instance.inventoryMarkData_SO.SaveAllData();
            VariableDictionaryManager.SaveData();
            //ItemSynthesisDictionary.SaveData();
            TeleportDictionary.instance.SaveData();
            ItemDictionary.instance.SaveWeaponLevel();
            Explore_Base.instance.SaveLanternData();
            ExploreMonsterManager.Instance.SaveAllMonsterDatas();
        }
        catch (Exception ex)
        {
            Debug.LogError($"保存游戏数据时出现异常: {ex.Message}");
        }
        finally
        {
            SavingFlag.instance.HideSavingFlag(1);
        }
    }


    /// <summary>
    /// 更新玩家位置
    /// </summary>
    public void SaveActorPosition(Vector3 PlayerPositon)
    {
        Vector3 pos = PlayerPositon;
        ES3.Save("playerPosition", pos);
    }


    /// <summary>
    /// 初始化角色的属性
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="SOData"></param>
    public void GameDataInitialize<T>(T SOData) where T : ScriptableObject
    {
        var actor = SOData;

        FieldInfo[] fields = actor.GetType().GetFields();
        foreach (FieldInfo field in fields)
        {
            ES3.Save(field.Name, field.GetValue(actor));
            Debug.Log(field.Name + ":" + field.GetValue(actor));
        }
    }


    /// <summary>
    /// 存储SO文件
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="SOData"></param>
    public void SaveSOData<T>(T SOData) where T : ScriptableObject
    {
        SafeSave<T>(SOData.name, SOData, SavePath);
        Debug.Log(SOData.name+"；地址："+ SavePath);
    }


    public void Login(int slotID)
    {
        //黑屏
        UIManager.instance.Show(UIManager.instance.LoadingPanel.gameObject, 0.3f);
        UIManager.instance.LoadingPanel.StartLoading();

        //更新存读档地址
        SaveSlot = slotID;
        SavePath = string.Format("SaveData/save0{0}.save", slotID);
        SetSavePath(slotID);
        //记录读取的存档ID
        ES3.Save("slotID", slotID, "playerSetting.save");

        //SO文件读档
        MainActorManager.instance.mainActorData_SO = LoadSOData(MainActorManager.instance.mainActorData_SO);
        MainActorManager.instance.mainActorData_SO.LoadAllData();
        int weaponID= ES3.TryLoad<int>("playerWeapon");
        int helmetID = ES3.TryLoad<int>("playerHelmet");
        int armorID = ES3.TryLoad<int>("playerArmor");
        if (weaponID >0) MainActorManager.instance.weaponData_SO = ItemDictionary.weaponDict[weaponID];
        if (helmetID >0) MainActorManager.instance.helmetData_SO = ItemDictionary.equipmentDict[helmetID];
        if (armorID >0) MainActorManager.instance.armorData_SO = ItemDictionary.equipmentDict[armorID];
        MainActorManager.instance.inventoryCardData_SO.LoadAllData();
        MainActorManager.instance.InventoryItemData_SO.LoadAllData();
        MainActorManager.instance.inventoryMarkData_SO.LoadAllData();
        MainActorManager.instance.inventoryEquipmentmentData_SO.LoadData();
        MainActorManager.instance.InventoryWeaponData_SO.LoadData();
        JobDictionary.instance.JobPathRank = ES3.Load<Dictionary<string, int> >("JobPathRank");
        Explore_Base.instance.LoadLanternData();

        //加载自组数据
        List<(int,int)> freeCardsData = ES3.Load<List<(int,int)>>("playerFreeDeck");
        List<WeaponDetails.CardDatas> freeDeck = new List<WeaponDetails.CardDatas>();
        foreach (var card in freeCardsData)
        {
            freeDeck.Add(new WeaponDetails.CardDatas(CardDictionary.instance.cardDict[card.Item1],false,card.Item2));
        }
        MainActorManager.instance.mainActorData_SO.freeDeck = freeDeck;

        ES3.Save("playerFreeDeck", freeCardsData);


        MainActorManager.instance.mainActorData_SO.LoadInherentCardData();
        MainActorManager.instance.mainActorData_SO.LoadPerfumesData();

        //加载全局变量
        VariableDictionaryManager.LoadData();

        //加载合成蓝图数据
        //ItemSynthesisDictionary.LoadData();

        //加载传送点数据
        TeleportDictionary.instance.LoadData();

        //加载武器强化等级
        ItemDictionary.instance.LoadWeaponLevel();

        //读取并加载场景
        string sceneName = ES3.Load<string>("sceneNow");
        Vector3 playerPosition = ES3.TryLoad<Vector3>("playerPosition");
        if (playerPosition == default)
            playerPosition = TeleportDictionary.instance.GetDefaultRebornPoint(sceneName).position;
        TeleportDictionary.instance.Teleport(sceneName,playerPosition,()=>
            {
                MainActorManager.instance.Initial();
                NomalActorBorn(sceneName);
                // 初始化游戏时长
                InitializePlayTime();
            },
            saving:false, setDeathRebornPoint:false
        );
    }

    /// <summary>
    /// 读取SO文件
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="SOData"></param>
    public T LoadSOData<T>(T SOData) where T : ScriptableObject
    {
        if (SOData==null) return ScriptableObject.CreateInstance<T>(); 
        var soData = ES3.Load<T>(SOData.name, SavePath);
        if (soData==null) soData = ScriptableObject.CreateInstance<T>(); 
        return soData;
    }


    public void NewDataCreate(string PlayName,JobSelector job)
    {
        //黑屏
        UIManager.instance.Show(UIManager.instance.LoadingPanel.gameObject, 0.3f);
        UIManager.instance.LoadingPanel.StartLoading();

        //获得职业属性并赋值给玩家       
        JobPath jobPath = job.selectedJobPath;
        // 保存初始职业（用于后续洗点时还原装备初始情况）
        NewDataCreate(PlayName, jobPath);

    }

    public void NewDataCreate(string PlayName, JobPath jobPath)
    {

        string initialJob = jobPath.jobPath;
        ES3.Save("initialJob", initialJob);

        //基础属性
        MainActorManager.instance.mainActorData_SO.playerName = PlayName;
        MainActorManager.instance.mainActorData_SO.playerLevel = 1;
        MainActorManager.instance.mainActorData_SO.playTime = 0;
        MainActorManager.instance.mainActorData_SO.upgradePoint = 0;
        MainActorManager.instance.mainActorData_SO.job = jobPath.jobPath;
        MainActorManager.instance.mainActorData_SO.InherentCardSlotCount = 1;
        MainActorManager.instance.mainActorData_SO.baseAttributes = new BaseAttributes
        {
            baseStrength = jobPath.STR,
            addStrength = 0,
            baseDexterity = jobPath.DEX,
            addDexterity = 0,
            baseIntelligence = jobPath.INT,
            addIntelligence = 0,
            baseStamina = jobPath.STA,
            addStamina = 0,
            baseFaith = jobPath.FAITH,
            addFaith = 0,
        };
        MainActorManager.instance.mainActorData_SO.battleAttributes = new BattleAttributes
        {
            DrawNum_base = 5,
            ap_base = 4,
            posture = 5
        };

        MainActorManager.instance.mainActorData_SO.perfumes = new List<ItemDataList_SO>();
        MainActorManager.instance.mainActorData_SO.SavePerfumesData();
        //初始话灯笼数据
        Explore_Base.instance.InitialLatenData();
        MainActorManager.instance.mainActorData_SO.InitialLanternUpgradeData();

        //解锁职业
        JobDictionary.instance.UnlockJob(jobPath.jobPath);

        //初始武器
        JobDictionary.instance.UnlockJob(jobPath.jobPath);//解锁职业

        //初始武器
        MainActorManager.instance.weaponData_SO = jobPath.initialWeapon;


        //初始卡片
        List<JobStage.JobCards> initialCards = jobPath.initialCards;
        MainActorManager.instance.mainActorData_SO.freeDeck.Clear();
        MainActorManager.instance.mainActorData_SO.InherentCard.Clear();
        List<WeaponDetails.CardDatas> freeDeck = new List<WeaponDetails.CardDatas>();
        foreach (var card in initialCards)
        {
            freeDeck.Add(new WeaponDetails.CardDatas(card.card, false, card.count));
        }
        MainActorManager.instance.mainActorData_SO.freeDeck = freeDeck;


        MainActorManager.instance.Initial();

        //背包初始化
        MainActorManager.instance.inventoryCardData_SO.inventoryCards.Clear();
        MainActorManager.instance.InventoryItemData_SO.equipedItemData.Clear();
        MainActorManager.instance.InventoryWeaponData_SO.weaponDatas.Clear();
        MainActorManager.instance.InventoryItemData_SO.inventoryItemData.Clear();

        MainActorManager.instance.inventoryMarkData_SO.RebuildEmptyInventoryMarkData();

        MainActorManager.instance.InventoryWeaponData_SO.weaponDatas.Add(new InventoryWeaponData_SO.InventoryWeaponData()
        {
            item = jobPath.initialWeapon
        });

        foreach (var card in MainActorManager.instance.mainActorData_SO.freeDeck)
        {
            InventoryCardData_SO.InventoryCardData inventoryCardData = new InventoryCardData_SO.InventoryCardData(card.cardData, card.cardCount);
            MainActorManager.instance.inventoryCardData_SO.inventoryCards.Add(inventoryCardData);
        }

        //初始刻印
        if (jobPath.initialMark != null)
            MainActorManager.instance.inventoryMarkData_SO.EquipeMark(jobPath.initialMark.markData.ID);

        // //初始化蓝图
        // ItemSynthesisDictionary.InitialEmpty();

        //初始化武器等级
        ItemDictionary.instance.InitialWeaponLevel();

        //其他属性初始化
        ES3.Save<Dictionary<string, int>>("JobPathRank", JobDictionary.instance.JobPathRank);

        //存储数据
        SaveSOData(MainActorManager.instance.mainActorData_SO);
        SaveSOData(MainActorManager.instance.weaponData_SO);
        SaveSOData(MainActorManager.instance.inventoryCardData_SO);
        SaveSOData(MainActorManager.instance.InventoryItemData_SO);
        SaveSOData(MainActorManager.instance.inventoryMarkData_SO);

        //存储初始卡数据
        List<(int, int)> freeCardsData = new List<(int, int)>();
        foreach (var card in MainActorManager.instance.mainActorData_SO.freeDeck)
        {
            freeCardsData.Add((card.cardData.cardDetails.cardID, card.cardCount));
        }
        ES3.Save("playerFreeDeck", freeCardsData);

        //生成玩家
        DOVirtual.DelayedCall(0.2f, null);
        SceneDictionary.instance.UnloadScene("CharacterCreate");
        GameManager.instance.interactionActor.gameObject.SetActive(true);//激活玩家角色
        CameraSystem.instance.ActivePlayerFollowCam();//激活玩家角色跟随相机
        CameraSystem.instance.CameraPostProcess(false);//关闭UICamera后处理



        TeleportDictionary.instance.Teleport("其他", "出生点", () =>
        {
            GameManager.instance.systemProcess = GameManager.SystemProcess.game;
            Explore_Base.instance.playerHpBar.gameObject.SetActive(true);//激活玩家血条
            MainActorManager.instance.HealHP(99999999);//给玩家回满血
            Explore_Base.instance.playerHpBar.UpdateHp();
            InteractionActor.instance.SetInteractionState(PlayerState.PlayerCurrentState.normal);
            EventManager.Instance.TriggerEvent(EventName.SAVE_DATA_LOADED);
            PlayerSpineAvatar.instance.RefreshPlayerSkin();
            // 初始化游戏时长
            InitializePlayTime();
        },
            saving: false, setDeathRebornPoint: true
        );
    }

    public void NomalActorBorn(string sceneName = "")
    {
        DOVirtual.DelayedCall(0.5f, null);
        GameManager.instance.interactionActor.gameObject.SetActive(true);//激活玩家角色
        CameraSystem.instance.ActivePlayerFollowCam();//激活玩家角色跟随相机
        CameraSystem.instance.CameraPostProcess(false);//关闭UICamera后处理
        SceneDictionary.instance.UnloadScene("CharacterCreate");
        Debug.Log("玩家生成");
        UIManager.instance.LoadingPanel.CancelLoading();
        UIManager.instance.Hide(UIManager.instance.LoadingPanel.gameObject,2f, 0.5f);
        GameManager.instance.systemProcess = GameManager.SystemProcess.game;
        EventManager.Instance.TriggerEvent(EventName.SAVE_DATA_LOADED);
        PlayerSpineAvatar.instance.RefreshPlayerSkin();//刷新玩家皮肤
        Explore_Base.instance.playerHpBar.gameObject.SetActive(true);//激活玩家血条
        InteractionActor.instance.SetInteractionState(PlayerState.PlayerCurrentState.normal);
        JobDictionary.RefreshJobUnlockStatus();//刷新职业解锁状态
        DOVirtual.DelayedCall(0.1f, () =>
        {
            ForceTeleportToLegalPoint(sceneName);
            Explore_Base.instance.playerHpBar.UpdateHp();
        });        
    }

    public void NormalActorReborn()
    {
        GameManager.instance.interactionActor.gameObject.SetActive(true);   //激活玩家角色
        GameManager.instance.interactionActor.playerState.SetNormal();      // 设置角色交互为normal状态
        CameraSystem.instance.ActivePlayerFollowCam();  //激活玩家角色跟随相机
        CameraSystem.instance.CameraPostProcess(false); //关闭UICamera后处理
        GameManager.instance.systemProcess = GameManager.SystemProcess.game;

        MainActorManager.instance?.Initial(); //初始化角色属性
        Explore_Base.instance.playerHpBar.gameObject.SetActive(true); //激活玩家血条
        DOVirtual.DelayedCall(0.1f,() => {
            Explore_Base.instance.playerHpBar.UpdateHp();
            SaveAllActorData();
        });
    }

    /// <summary>
    /// 存档是否存在
    /// </summary>
    /// <param name="savePath"></param>
    /// <returns></returns>
    public bool SaveDataExist(string savePath)
    {
        string path = Path.Combine(Application.persistentDataPath, savePath);
        if (File.Exists(path))
        {
            Debug.Log("存档存在："+ savePath);
            return true;
        }
        Debug.Log("存档不存在");
        return false;
    }

    public void DeleteSaveData(int slotID)
    {
        string path = Path.Combine(Application.persistentDataPath, string.Format("SaveData/save0{0}.save", slotID));
        if (SaveDataExist(path))
        {
            File.Delete(path);
            Debug.Log("存档已删除");
        }
        else
        {
            return;
        }
    }

    public void ForceTeleportToLegalPoint(string sceneName)
    {
        // if (!InteractionActor.instance.stayInArea)
        // {
        //     Debug.Log("玩家当前不在任何交互区内");
        //     var playerPosition = TeleportDictionary.instance.GetDefaultRebornPoint(sceneName).position;
        //     TeleportDictionary.instance.Teleport(sceneName, playerPosition);
        // }
    }

    private void UpdatePlayTimeToMainActorData()
    {
        // 累计总游戏时长并转换为整数（秒）
        totalPlayTime += currentSessionPlayTime;
        MainActorManager.instance.mainActorData_SO.playTime = Mathf.FloorToInt(totalPlayTime);
        
        // 重置当前会话时间，但保持计时状态
        currentSessionPlayTime = 0f;
    }

    private void InitializePlayTime()
    {
        // 初始化当前会话游戏时长
        currentSessionPlayTime = 0f;
        
        // 从存档中读取已存储的游戏时长
        int savedPlayTime = MainActorManager.instance.mainActorData_SO.playTime;
        totalPlayTime = savedPlayTime;
        
        // 添加日志说明时长初始化情况
        if (savedPlayTime == 0)
        {
            Debug.Log("存档中没有游戏时长信息或时长为0，从现在开始计时");
        }
        else
        {
            Debug.Log($"从存档读取游戏时长：{savedPlayTime}秒 ({GetFormattedTotalTime(savedPlayTime)})");
        }
    }
    
    // 辅助方法：将秒数格式化为时间字符串
    private string GetFormattedTotalTime(int totalSeconds)
    {
        return FormatTimeFromSeconds(totalSeconds);
    }

    // 获取当前总游戏时长（秒）
    public int GetTotalPlayTime()
    {
        return Mathf.FloorToInt(totalPlayTime + currentSessionPlayTime);
    }

    // 获取当前会话游戏时长（秒）
    public int GetCurrentSessionPlayTime()
    {
        return Mathf.FloorToInt(currentSessionPlayTime);
    }

    // 获取格式化的游戏时长字符串
    public string GetFormattedPlayTime()
    {
        int totalSeconds = GetTotalPlayTime();
        return FormatTimeFromSeconds(totalSeconds);
    }

    // 静态方法：将任意秒数格式化为时间字符串（供其他类使用）
    public static string FormatTimeFromSeconds(int totalSeconds)
    {
        int hours = totalSeconds / 3600;
        int minutes = (totalSeconds % 3600) / 60;
        int seconds = totalSeconds % 60;
        
        if (hours > 0)
            return $"{hours:D2}:{minutes:D2}:{seconds:D2}";
        else
            return $"{minutes:D2}:{seconds:D2}";
    }
}
