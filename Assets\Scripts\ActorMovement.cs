using Sirenix.OdinInspector;
using UnityEngine;
using Spine.Unity;
using Unity.Mathematics;
using System;

public class ActorMovement : MonoBehaviour
{
    public float walkSpeed = 3f;
    private float originalWalkSpeed;
    public float climbingSpeed = 6f;
    public float runSpeed = 8f;
    private float originalRunSpeed;
    public LayerMask groundLayer;
    public float gravity = -9.8f;
    private Vector3 velocity = Vector3.zero;
    public float groundCheckDistance = 0.2f;
    public GameObject spineObject;
    public Transform Foot;
    [ LabelText("相机瞄准点")]
    public Transform cameraPoint;
    [ LabelText("相机向下偏移量")]
    public Vector3 cameraOffset = new Vector3(0, 0.3f, -4);
    [ LabelText("相机偏移插值速度")]
    public float cameraOffsetLerpSpeed = 5f;
    private Vector3 originalCameraPointLocalPosition;
    [SerializeField]
    public AnimationCurve speedCurve = AnimationCurve.EaseInOut(0, 0, 1, 1); // 定义速度变化曲线
    public float walkCycleDuration = 1f; // 行走动画的步伐周期时间
    public float runCycleDuration = 0.8f; // 跑步动画的步伐周期时间
    public FootstepSoundConfig footstepSoundConfig;
    [LabelText("最小增幅百分比"), TitleGroup("脚步声随机音量")]
    public float minVolume = -20.0f;
    [LabelText("最大增幅百分比")]
    public float maxVolume = 20.0f;
    private float curveTime = 0f; // 用于跟踪曲线时间
    public void ResetCurveTime() => curveTime = 0f; 

    public enum PlayerMovementState
    {
        Idle,
        MovingForward,
        MovingBackward,
        MovingLeft,
        MovingForward_Left,
        MovingRight,
        MovingForward_Right,
        RuningForward,
        RuningBackward,
        RuningLeft,
        RuningForward_Left,
        RuningRight,
        RuningForward_Right,

        Sliding,
        Landing,
        Climbhood,
        Climbing,
        IdleForward
    }

    [ShowInInspector, ReadOnly]
    public PlayerMovementState currentState
    {
        get { return m_currentState; }
        set
        {
            if (m_currentState != value)
            {
                m_currentState = value;
                if (m_currentState == PlayerMovementState.Climbing||m_currentState == PlayerMovementState.Climbhood)
                {
                    moveDirection = Vector3.zero;
                }
            }
        }
    }

    private PlayerMovementState m_currentState = PlayerMovementState.Idle;
    public PlayerState ps;
    private CharacterController characterController;
    private Vector3 moveDirection;
    [ShowInInspector, ReadOnly]
    private bool isGrounded;

    void Start()
    {
        ps = GetComponent<PlayerState>();
        characterController = GetComponent<CharacterController>();
        if (spineObject == null)
        {
            spineObject = transform.GetComponentInChildren<SkeletonAnimation>().gameObject;
            if (spineObject == null)
            {
                Debug.Log("spineObject不存在");
            }
        }

        canWalk = true;
        originalRunSpeed = runSpeed;
        originalWalkSpeed = walkSpeed;
        originalCameraPointLocalPosition = cameraPoint.localPosition; // 记录初始本地位置
    }

    void Update()
    {
        CheckUIOpened();
        PlayerStateWatching();
        UpdateCameraPointPosition(); // 添加相机位置更新逻辑
        // 根据玩家的状态使用不同的速度

    }

    private bool _isRunning;
    private bool _isWalking;
    private bool isConstrained;
    
    void FixedUpdate()
    {
        if (ps.State == PlayerState.PlayerCurrentState.jumping)
            return;
        
        _isRunning = currentState.ToString().StartsWith("Runing");
        _isWalking = currentState.ToString().StartsWith("Moving");
        
        float speed = 0;
        // 赋值speed
        if (_isRunning) speed = runSpeed;
        else if (_isWalking) speed = walkSpeed;
        
        // 判断移动受限
        isConstrained = speed < originalWalkSpeed;
        
        float cycleDuration = _isRunning ? runCycleDuration : walkCycleDuration;
        // 赋值cycleDuration
        if (_isRunning) cycleDuration = runCycleDuration;
        else if (_isWalking) cycleDuration = walkCycleDuration;
        
        float speedMultiplier = speedCurve.Evaluate(curveTime / cycleDuration);

        // 应用移动
        Vector3 movement = moveDirection * speed * speedMultiplier;
        
        // 在斜坡上的处理
        if (isGrounded)
        {
            RaycastHit hit;
            if (Physics.Raycast(transform.position, Vector3.down, out hit, groundCheckDistance + 0.1f, groundLayer))
            {
                float slopeAngle = Vector3.Angle(hit.normal, Vector3.up);
                
                if (slopeAngle > 0)
                {
                    // 将移动方向投影到斜坡表面
                    movement = Vector3.ProjectOnPlane(movement, hit.normal).normalized * movement.magnitude;
                    
                    // 完全消除累积的速度
                    velocity = Vector3.zero;
                    
                    // 只有当玩家主动移动时才应用移动
                    if (moveDirection.magnitude < 0.1f)
                    {
                        // 当玩家静止时，不添加任何额外的力
                        movement = Vector3.zero;
                    }
                }
            }
        }
        
        characterController.Move(movement * Time.fixedDeltaTime);

        // 只在非地面状态应用重力
        if (!isGrounded && ps.State != PlayerState.PlayerCurrentState.climbing)
        {
            velocity.y += gravity * Time.fixedDeltaTime;
            characterController.Move(velocity * Time.fixedDeltaTime);
        }
        else
        {
            velocity = Vector3.zero;
        }
    }

    /// <summary>
    /// 更改玩家移动速度。-1代表还原为原来的速度
    /// </summary>
    /// <param name="newWalkSpeed"></param>
    /// <param name="newRunSpeed"></param>
    public void ChangeMoveSpeed(float newWalkSpeed = -1, float newRunSpeed = -1, bool forbidSwitch = false)
    {
        InteractionActor.instance.WalkingStateSwitchable(!forbidSwitch);
        walkSpeed = newWalkSpeed != -1 ? newWalkSpeed : originalWalkSpeed;
        runSpeed = newRunSpeed != -1 ? newRunSpeed : originalRunSpeed;
    }

    private void Climbing()
    {
        
        CheckGrounded();
        float vertical = Input.GetAxis("Vertical");

        if (ps.State == PlayerState.PlayerCurrentState.climbing) 
        {
            currentState =  GetClimbingState(vertical);
        }

        // 当玩家在地面上且按向下时，将爬梯子的上下移动逻辑替换为行走的前后移动逻辑
        if (isGrounded && vertical < 0)
        {
            Walking();
        }
        else if (Input.GetButton("Jump"))
        {
            Vector3 climbDirection = new Vector3(0, -1, 0);
            characterController.Move(climbDirection * climbingSpeed *5f* Time.deltaTime);
        }
        else
        {
            if (!AudioManager.Instance.IsSePlaying() && currentState == PlayerMovementState.Climbing)
                AudioManager.Instance.PlaySingleSE("爬梯子");
            Vector3 climbDirection = new Vector3(0, vertical * climbingSpeed, 0);
            characterController.Move(climbDirection * climbingSpeed * Time.deltaTime);
        }
    }

    private float walkingTime = 0f;

    public enum CurrentMovingState { Walking = 0, Running = 1,};

    // 是否可以操控行走
    public bool canWalk = true;

    private void Walking()
    {
        if (!canWalk) return;
        
        CheckGrounded();
        HandleInput();
        UpdateWalkingTime();
        bool isRunning = false;
        switch (InteractionActor.instance.currentMovingState)
        {
            case CurrentMovingState.Running:
                if ((originalRunSpeed * 2/3) >= runSpeed)
                {
                    InteractionActor.instance.switchRun = false;
                    isRunning = false;
                }
                else
                {
                    InteractionActor.instance.WalkingStateSwitchable(true);
                    isRunning = InteractionActor.instance.switchRun;
                }
                break;
            case CurrentMovingState.Walking:
                if ((originalWalkSpeed * 1.5f) <= walkSpeed)
                {
                    InteractionActor.instance.switchRun = true;
                    isRunning = true;
                }
                else
                {
                    InteractionActor.instance.WalkingStateSwitchable(true);
                    isRunning = InteractionActor.instance.switchRun;
                }
                break;
        }
        // if ((originalRunSpeed * 2/3) >= runSpeed || (originalWalkSpeed * 1.5f) <= walkSpeed)
        //     InteractionActor.instance.switchRun = !InteractionActor.instance.switchRun;
        UpdateMovementState(isRunning);
    }

    private void HandleInput()
    {
        float horizontalInput = Input.GetAxis("Horizontal");
        float verticalInput = Input.GetAxis("Vertical");

        moveDirection = CalculateMoveDirection(horizontalInput, verticalInput);
        UpdateSpineDirection(horizontalInput);
    }

    private Vector3 CalculateMoveDirection(float horizontalInput, float verticalInput)
    {
        Vector3 forwardDirection = Camera.main.transform.forward;
        Vector3 rightDirection = Camera.main.transform.right;

        forwardDirection.y = 0;
        rightDirection.y = 0;

        forwardDirection.Normalize();
        rightDirection.Normalize();
        
        moveDirection.x = forwardDirection.x * verticalInput + rightDirection.x * horizontalInput;
        moveDirection.z = forwardDirection.z * verticalInput + rightDirection.z * horizontalInput;

        // //当单方向移动时，使用rootMotion;
        // if (moveDirection.x == 0 && moveDirection.z != 0) return Vector3.zero;
        // if (moveDirection.z == 0 && moveDirection.x != 0) return Vector3.zero;
        moveDirection.Normalize();

        return moveDirection;
    }

    public float LanternAccumulation => lanternAccumulation;
    [SerializeField, LabelText("提灯累积间隔")] private float lanternAccumulation = 3f;

    private void UpdateWalkingTime()
    {
        float horizontalInput = Input.GetAxis("Horizontal");
        float verticalInput = Input.GetAxis("Vertical");

        if (horizontalInput != 0 || verticalInput != 0)
        {
            walkingTime += Time.deltaTime;
        }

        if (walkingTime > LanternAccumulation)
        {
            walkingTime -= walkingTime;
            Explore_Base.instance.playerWalkingOneSec();
        }
    }

    private void UpdateMovementState(bool isRunning)
    {
        float horizontalInput = Input.GetAxis("Horizontal");
        float verticalInput = Input.GetAxis("Vertical");

        if (isGrounded)
        {
            currentState = GetMovementState(horizontalInput, verticalInput, isRunning);
        }
        else
        {
            //currentState = PlayerMovementState.Sliding;
        }
    }

    private PlayerMovementState GetMovementState(float horizontalInput, float verticalInput, bool isRunning)
    {
        if (horizontalInput == 0 && verticalInput == 0)
        {
            if (currentState == PlayerMovementState.IdleForward)
            {
                return PlayerMovementState.IdleForward;
            }
            if (currentState == PlayerMovementState.MovingForward || 
                currentState == PlayerMovementState.RuningForward)
            {
                return PlayerMovementState.IdleForward;
            }
            return PlayerMovementState.Idle;
        }
        else if (isRunning)
        {
            return GetRunningState(horizontalInput, verticalInput);
        }
        else
        {
            return GetWalkingState(horizontalInput, verticalInput);
        }
    }

    private PlayerMovementState GetClimbingState(float verticalInput)
    {
       if (verticalInput==0)
       {
           return currentState = PlayerMovementState.Climbhood;
       }
       else
       {
           return currentState = PlayerMovementState.Climbing;
       }
    }

    private PlayerMovementState GetRunningState(float horizontalInput, float verticalInput)
    {
        if (verticalInput > 0 && horizontalInput == 0)
        {
            return currentState = PlayerMovementState.RuningForward;
        }
        else if (verticalInput < 0 && horizontalInput == 0)
        {
            return currentState = PlayerMovementState.RuningBackward;
        }
        else if (verticalInput > 0 && horizontalInput > 0)
        {
            return currentState = PlayerMovementState.RuningForward_Right;
        }
        else if (verticalInput > 0 && horizontalInput < 0)
        {
            return currentState = PlayerMovementState.RuningForward_Left;
        }
        else if (horizontalInput > 0)
        {
            return currentState = PlayerMovementState.RuningRight;
        }
        else if (horizontalInput < 0)
        {
            return currentState = PlayerMovementState.RuningLeft;
        }
        else
        {
            return currentState = PlayerMovementState.Idle;
        }
    }

    private PlayerMovementState GetWalkingState(float horizontalInput, float verticalInput)
    {
        if (verticalInput > 0 && horizontalInput == 0)
        {
            return currentState = PlayerMovementState.MovingForward;
        }
        else if (verticalInput < 0 && horizontalInput == 0)
        {
            return currentState = PlayerMovementState.MovingBackward;

        }
        else if (horizontalInput > 0)
        {
            return currentState = PlayerMovementState.MovingRight;
        }
        else if (horizontalInput < 0)
        {
            return currentState = PlayerMovementState.MovingLeft;
        }
        else
        {
            return currentState = PlayerMovementState.Idle;
        }
    }

    private void UpdateSpineDirection(float horizontalInput)
    {
        if (horizontalInput > 0) spineObject.transform.localScale = new Vector3(math.abs(spineObject.transform.localScale.x),
        spineObject.transform.localScale.y, spineObject.transform.localScale.z);
        if (currentState == PlayerMovementState.MovingBackward||currentState == PlayerMovementState.MovingForward) 
        {
            spineObject.transform.localScale = new Vector3(math.abs(spineObject.transform.localScale.x),
                       spineObject.transform.localScale.y, spineObject.transform.localScale.z);
           return;
        }
        if (horizontalInput < 0) spineObject.transform.localScale = new Vector3(-math.abs(spineObject.transform.localScale.x),
            spineObject.transform.localScale.y, spineObject.transform.localScale.z);
    }


    void CheckGrounded()
    {
        isGrounded = Physics.CheckSphere(Foot.position, groundCheckDistance, groundLayer);
    }

    private void PlayerStateWatching()
    {
        switch (ps.State)
        {
            case PlayerState.PlayerCurrentState.normal:
                Walking();
                break;
            case PlayerState.PlayerCurrentState.battle:
                moveDirection = Vector3.zero;
                currentState = PlayerMovementState.Idle;
                break;
            case PlayerState.PlayerCurrentState.talking:
                moveDirection = Vector3.zero;
                currentState = PlayerMovementState.Idle;
                break;
            case PlayerState.PlayerCurrentState.climbing:
                Climbing();
                break;
            case PlayerState.PlayerCurrentState.CantControl:
                moveDirection = Vector3.zero;
                currentState = PlayerMovementState.Idle;
                break;
            case PlayerState.PlayerCurrentState.jumping:
                moveDirection = Vector3.zero;
                currentState = PlayerMovementState.Landing;
                break;
        }
    }

    private void CheckUIOpened()
    {
        if (UIManager.instance.activedUI != null)
        {
            moveDirection = Vector3.zero;
            currentState = PlayerMovementState.Idle;
            return;
        }
    }

    private bool _currentLeftStep;
    
    public void OnLeftStep()
    {
        if (!_currentLeftStep)
            return;
        
        _currentLeftStep = false;
        
        if (_isWalking)
        {
            if (isConstrained)
                footstepSoundConfig.LeftConstrain();
            else
                footstepSoundConfig.LeftWalk();
        }
        else if (_isRunning)
            footstepSoundConfig.LeftRun();
    }
    
    public void OnRightStep()
    {
        if (_currentLeftStep)
            return;
        
        _currentLeftStep = true;
        
        if (_isWalking)
        {
            if (isConstrained)
                footstepSoundConfig.RightConstrain();
            else
                footstepSoundConfig.RightWalk();
        }
        else if (_isRunning)
            footstepSoundConfig.RightRun();
    }

    // 新增方法：更新相机瞄准点位置
    private void UpdateCameraPointPosition()
    {
        float verticalInput = Input.GetAxis("Vertical"); // 获取垂直输入

        Vector3 targetLocalPosition = originalCameraPointLocalPosition; // 默认目标位置为初始位置

        // 如果正在向下移动 (verticalInput < 0 并且玩家处于可以行走或跑动的状态)
        if (verticalInput < 0 && (currentState == PlayerMovementState.MovingBackward || currentState == PlayerMovementState.RuningBackward))
        {
            targetLocalPosition = originalCameraPointLocalPosition + cameraOffset; // 直接加上 Vector3 偏移量
        }

        // 使用 Lerp 平滑过渡到目标位置
        cameraPoint.localPosition = Vector3.Lerp(cameraPoint.localPosition, targetLocalPosition, Time.deltaTime * cameraOffsetLerpSpeed);
    }
}
